{"rustc": 1842507548689473721, "features": "[\"approx\", \"blas\", \"cblas-sys\", \"default\", \"libc\", \"rayon\", \"rayon_\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 2241668132362809309, "path": 2525462169438355074, "deps": [[2289341005599476083, "approx", false, 11827345385470238255], [4684437522915235464, "libc", false, 9685844023081248841], [5157631553186200874, "num_traits", false, 8171727555973592493], [6959212579930910503, "cblas_sys", false, 11452793412667640189], [10697383615564341592, "rayon_", false, 8266772663150952867], [12319020793864570031, "num_complex", false, 8410273256261348971], [15709748443193639506, "rawpointer", false, 6928979023607731060], [15826188163127377936, "matrixmultiply", false, 16923028931088197222], [16795989132585092538, "num_integer", false, 4833666588563926257]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ndarray-9cefa989d67dda8a\\dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}