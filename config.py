"""
Configuration settings for Dynamic FX Clustering Application
Timezone and MT5 connection settings following matrix_QP patterns
"""

import MetaTrader5 as mt5
import pytz
from datetime import datetime, timedelta
from typing import List

# =============================================================================
# CURRENCY PAIRS CONFIGURATION
# =============================================================================

# Major currency pairs for clustering analysis
CURRENCY_PAIRS = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 'AUDUSD', 'NZDUSD',
    'EURJPY', 'EURGBP', 'EURCHF', 'EURAUD', 'EURNZD', 'EURCAD',
    'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPNZD', 'GBPCAD',
    'AUDJPY', 'AUDCHF', 'AUDCAD', 'AUDNZD',
    'NZDJPY', 'NZDCHF', 'NZDCAD',
    'CADJPY', 'CADCHF', 'CHFJPY'
]

# =============================================================================
# MT5 CONNECTION CONFIGURATION
# =============================================================================

# MT5 timeframe for data collection
TIMEFRAME = mt5.TIMEFRAME_M1  # 1-minute bars for maximum data resolution

# Connection settings
MT5_TIMEOUT = 30  # Connection timeout in seconds
MAX_RETRIES = 3   # Maximum connection retry attempts
RETRY_DELAY = 5   # Delay between retries in seconds

# Data collection settings
START_OF_DAY_HOUR = 0  # Market day starts at 00:00
DATA_BUFFER_HOURS = 2  # Extra hours to ensure complete data

# =============================================================================
# TIMEZONE CONFIGURATION
# =============================================================================

# Primary timezone for market operations (following matrix_QP pattern)
MARKET_TIMEZONE = pytz.timezone('Europe/Bucharest')
UTC_TIMEZONE = pytz.UTC

# Trading session times (in market timezone)
TRADING_START_HOUR = 0   # 00:00
TRADING_END_HOUR = 23    # 23:59

# =============================================================================
# CLUSTERING CONFIGURATION
# =============================================================================

# Correlation clustering settings
DEFAULT_CORRELATION_THRESHOLD = 0.7
MIN_CLUSTER_SIZE = 2
MAX_CLUSTERS = 10

# Volatility regime clustering settings
DEFAULT_VOLATILITY_CLUSTERS = 4
MIN_DAYS_FOR_CLUSTERING = 7
MIN_DATA_COMPLETENESS = 0.8

# Clustering algorithm parameters
CLUSTERING_MAX_ITERATIONS = 300
CLUSTERING_TOLERANCE = 1e-4
CLUSTERING_RANDOM_SEED = 42

# =============================================================================
# DASHBOARD CONFIGURATION
# =============================================================================

# Update intervals (in milliseconds)
DATA_UPDATE_INTERVAL = 300000  # 5 minutes
CHART_UPDATE_INTERVAL = 60000   # 1 minute

# Chart styling
CHART_THEME = 'plotly_dark'
CHART_HEIGHT = 500
CHART_WIDTH = 1400

# Color schemes for clustering visualization
CLUSTER_COLORS = [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
]

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging levels
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def get_market_start_time():
    """Get the start of current market day in market timezone"""
    now = datetime.now(MARKET_TIMEZONE)
    
    # Always use today's start at 00:00
    start_of_day = now.replace(hour=START_OF_DAY_HOUR, minute=0, second=0, microsecond=0)
    
    # If it's weekend, get Friday's start
    if now.weekday() >= 5:  # Saturday or Sunday
        days_back = now.weekday() - 4  # Days back to Friday
        start_of_day = start_of_day - timedelta(days=days_back)
    
    return start_of_day

def get_market_end_time():
    """Get the end of current market day in market timezone"""
    now = datetime.now(MARKET_TIMEZONE)
    
    # If it's weekend, get Friday's end
    if now.weekday() >= 5:  # Saturday or Sunday
        days_back = now.weekday() - 4  # Days back to Friday
        end_of_day = now - timedelta(days=days_back)
        end_of_day = end_of_day.replace(hour=TRADING_END_HOUR, minute=59, second=59, microsecond=0)
    else:
        # Current day end
        end_of_day = now.replace(hour=TRADING_END_HOUR, minute=59, second=59, microsecond=0)
    
    return end_of_day

def is_market_open():
    """Check if market is currently open (not weekend)"""
    now = datetime.now(MARKET_TIMEZONE)
    return now.weekday() < 5  # Monday=0 to Friday=4

def get_timezone_offset(timezone_hours: int = 0):
    """Get timezone offset for display purposes"""
    if timezone_hours == 0:
        return UTC_TIMEZONE
    else:
        # Create timezone with offset
        offset = timedelta(hours=timezone_hours)
        return pytz.FixedOffset(timezone_hours * 60)
