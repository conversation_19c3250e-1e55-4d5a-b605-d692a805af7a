"""
Configuration module for Matrix QP Portfolio Optimization
Contains all constants, settings, and configuration parameters
"""

import MetaTrader5 as mt5
from datetime import datetime, timedelta
import pytz

# =============================================================================
# CURRENCY PAIRS CONFIGURATION
# =============================================================================

# The 28 major forex pairs for portfolio optimization
CURRENCY_PAIRS = [
    # EUR pairs
    "EURUSD", "EURGBP", "EURAUD", "EURNZD", "EURCHF", "EURCAD", "EURJPY",
    # GBP pairs  
    "GBPUSD", "GBPAUD", "GBPNZD", "GBPCHF", "GBPCAD", "GBPJPY",
    # AUD pairs
    "AUDUSD", "AUDNZD", "AUDCHF", "AUDCAD", "AUDJPY", 
    # NZD pairs
    "NZDUSD", "NZDCHF", "NZDCAD", "NZDJPY",
    # USD pairs
    "USDCHF", "USDCAD", "USDJPY",
    # Cross pairs
    "CADCHF", "CADJPY", "CHFJPY"
]

# Currency codes for distribution analysis
CURRENCIES = ["EUR", "USD", "GBP", "AUD", "NZD", "CAD", "CHF", "JPY"]

# =============================================================================
# PORTFOLIO OPTIMIZATION PARAMETERS
# =============================================================================

# Portfolio constraints
PORTFOLIO_SIZE = 6  # Exactly 6 pairs per portfolio
MIN_PORTFOLIOS = 3  # Minimum number of portfolios to present
MAX_CURRENCY_OCCURRENCE = 3  # Max times a currency can appear as base or quote in a single portfolio
MAX_CURRENCY_TOTAL_OCCURRENCE = 2  # Max times a currency can appear total (base + quote) in a single portfolio

# Optimization strategies
OPTIMIZATION_STRATEGIES = [
    "max_sharpe",      # Maximum Sharpe ratio
    "max_sortino",     # Maximum Sortino ratio
    "max_omega",       # Maximum Omega ratio
    "max_calmar",      # Maximum Calmar ratio
    "min_variance"     # Minimum variance
]

# Weight constraints
MIN_WEIGHT = 0.05   # Minimum absolute weight (5% - enforced during optimization)
MAX_WEIGHT = 0.50   # Maximum absolute weight (50%)
WEIGHT_TOLERANCE = 1e-6  # Tolerance for weight normalization

# Portfolio diversification settings
ENABLE_PORTFOLIO_DIVERSIFICATION = True  # Enable diversification across strategies
MAX_PAIR_REUSE_WEIGHT = 0.30  # Maximum weight for reused pairs in subsequent strategies (30%)
DIVERSIFICATION_THRESHOLD = 0.20  # Minimum weight threshold to consider a pair as "significant" (20%)

# Cross-portfolio pair appearance limits
MAX_PAIR_APPEARANCES = 3  # Maximum times a single pair can appear across all portfolios
                         # Set to 1 for maximum diversity (each pair appears once)
                         # Set to 2 for balanced diversity (recommended)
                         # Set to 3+ for moderate diversity
ENABLE_PAIR_APPEARANCE_LIMIT = True  # Enable limiting pair appearances across portfolios
ENSURE_STRATEGY_REPRESENTATION = True  # Ensure at least one portfolio per strategy (may relax pair limits)

# =============================================================================
# RISK CALCULATION PARAMETERS
# =============================================================================

# Risk metrics configuration
CONFIDENCE_LEVELS = [0.95, 0.99]  # For VaR/CVaR calculations
RISK_FREE_RATE = 0.0  # Assumed risk-free rate
TRADING_DAYS_PER_YEAR = 252

# Volatility calculation method
USE_CVAR_FOR_VOLATILITY = True  # Use standard deviation for volatility (matches matrix_mpt)
CVAR_CONFIDENCE_LEVEL = 0.95     # Confidence level for CVaR volatility calculation

# Drawdown calculation
MAX_DRAWDOWN_THRESHOLD = 0.20  # 20% maximum acceptable drawdown
VOLATILITY_THRESHOLD = 0.15    # 15% maximum acceptable volatility

# =============================================================================
# MT5 CONNECTION PARAMETERS
# =============================================================================

# MT5 timeframe for data collection
TIMEFRAME = mt5.TIMEFRAME_M1  # 1-minute bars for maximum data resolution

# Connection settings
MT5_TIMEOUT = 30  # Connection timeout in seconds
MAX_RETRIES = 3   # Maximum connection retry attempts
RETRY_DELAY = 5   # Delay between retries in seconds

# Data collection settings
START_OF_DAY_HOUR = 0  # Market day starts at 00:00
DATA_BUFFER_HOURS = 2  # Extra hours to ensure complete data

# =============================================================================
# TIMEZONE CONFIGURATION
# =============================================================================

# Primary timezone for market operations
MARKET_TIMEZONE = pytz.timezone('Europe/Bucharest')
UTC_TIMEZONE = pytz.UTC

# Trading session times (in market timezone)
TRADING_START_HOUR = 0   # 00:00
TRADING_END_HOUR = 23    # 23:59

# =============================================================================
# OPTIMIZATION ALGORITHM PARAMETERS
# =============================================================================

# Two-stage optimization settings
ENABLE_TWO_STAGE_OPTIMIZATION = True  # Enable fast pre-screening + refined optimization

# Stage 1: Fast pre-screening to filter out poor combinations
STAGE1_MAX_ITERATIONS = 1000           # Fast screening with low iterations
STAGE1_TOLERANCE = 1e-4               # Relaxed tolerance for speed
STAGE1_TOP_COMBINATIONS_RATIO = 0.2   # Keep top 20% of combinations for stage 2

# Stage 2: Refined optimization for best candidates
STAGE2_MAX_ITERATIONS = 20000         # High iterations for best results
STAGE2_TOLERANCE = 1e-8               # Tight tolerance for precision

# Legacy single-stage settings (used when two-stage is disabled)
OPTIMIZATION_METHOD = 'SLSQP'  # Sequential Least Squares Programming
MAX_ITERATIONS = 1000          # Default iterations (overridden by two-stage)
OPTIMIZATION_TOLERANCE = 1e-8  # Default tolerance (overridden by two-stage)

# Parallel processing
MAX_WORKERS = 4  # Maximum number of parallel workers (legacy)
BATCH_SIZE = 100  # Batch size for parallel processing

# Joblib multiprocessing settings (preferred over concurrent.futures)
USE_JOBLIB_MULTIPROCESSING = True  # Use joblib instead of concurrent.futures
JOBLIB_BACKEND = 'loky'           # Backend: 'loky' (recommended), 'multiprocessing', 'threading'
JOBLIB_VERBOSE = 1                # Verbosity level (0=silent, 1=progress, 10=detailed)
JOBLIB_MAX_NBYTES = '50M'         # Memory limit per job
JOBLIB_BATCH_SIZE = 'auto'        # Batch size ('auto' or integer)

# Joblib multiprocessing settings
USE_JOBLIB_MULTIPROCESSING = True  # Use joblib instead of concurrent.futures
JOBLIB_BACKEND = 'loky'           # Backend: 'loky', 'multiprocessing', 'threading'
JOBLIB_N_JOBS = -1                # Number of jobs (-1 = all available cores)
JOBLIB_BATCH_SIZE = 'auto'        # Batch size for joblib ('auto' or integer)
JOBLIB_VERBOSE = 1                # Verbosity level (0=silent, 1=progress, 2=detailed)

# =============================================================================
# DASHBOARD CONFIGURATION
# =============================================================================

# Dash application settings
DASH_HOST = '127.0.0.1'
DASH_PORT = 8060
DASH_DEBUG = True

# Update intervals (in milliseconds)
PORTFOLIO_UPDATE_INTERVAL = 300000  # 5 minutes
RISK_UPDATE_INTERVAL = 300000       # 5 minutes
DATA_UPDATE_INTERVAL = 300000       # 5 minutes

# Chart styling
CHART_THEME = 'plotly_dark'
CHART_HEIGHT = 500
CHART_WIDTH = 1400

# Color schemes
COLORS = {
    'primary': '#1f77b4',
    'secondary': '#ff7f0e',
    'success': '#2ca02c',
    'danger': '#d62728',
    'warning': '#ff7f0e',
    'info': '#17a2b8',
    'background': '#2c3e50',
    'text': '#ecf0f1'
}

# Currency-specific colors for pair visualization
# Color is determined by which currency has positive log returns
# Using named colors to match Matrix E implementation
CURRENCY_COLORS = {
    'EUR': 'dodgerblue',  # Blue
    'GBP': 'red',         # Red
    'AUD': 'orange',      # Orange
    'NZD': 'aqua',        # Aqua/Cyan
    'USD': 'lime',        # Green (lime like Matrix E)
    'CAD': 'fuchsia',     # Pink (fuchsia like Matrix E)
    'JPY': 'yellow',      # Yellow
    'CHF': 'silver'       # Gray (silver like Matrix E)
}

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging levels and format
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_FILE = 'matrix_qp.log'

# =============================================================================
# PERFORMANCE MONITORING
# =============================================================================

# CPU and memory thresholds
MAX_CPU_USAGE = 80  # Maximum CPU usage percentage
MAX_MEMORY_USAGE = 85  # Maximum memory usage percentage

# Timing thresholds (in seconds)
MAX_OPTIMIZATION_TIME = 60  # Maximum time for portfolio optimization
MAX_DATA_FETCH_TIME = 30    # Maximum time for data fetching

# =============================================================================
# VALIDATION PARAMETERS
# =============================================================================

# Data quality checks
MIN_DATA_POINTS = 30  # Minimum data points for optimization (reduced for intraday)
MAX_MISSING_DATA_RATIO = 0.05  # Maximum 5% missing data allowed

# Portfolio validation
MIN_SHARPE_RATIO = 0.5   # Minimum acceptable Sharpe ratio
MIN_SORTINO_RATIO = 0.7  # Minimum acceptable Sortino ratio
MAX_CORRELATION = 0.8    # Maximum correlation between portfolio pairs

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def get_market_start_time():
    """Get the start of current market day in market timezone"""
    now = datetime.now(MARKET_TIMEZONE)

    # Always use today's start at 00:00
    start_of_day = now.replace(hour=START_OF_DAY_HOUR, minute=0, second=0, microsecond=0)

    # If it's weekend, get Friday's start
    if now.weekday() >= 5:  # Saturday or Sunday
        days_back = now.weekday() - 4  # Days back to Friday
        start_of_day = start_of_day - timedelta(days=days_back)

    return start_of_day

def get_currency_from_pair(pair, position='base'):
    """Extract base or quote currency from a currency pair"""
    if len(pair) != 6:
        raise ValueError(f"Invalid currency pair format: {pair}")
    
    if position == 'base':
        return pair[:3]
    elif position == 'quote':
        return pair[3:6]
    else:
        raise ValueError("Position must be 'base' or 'quote'")

def validate_portfolio_distribution(pairs):
    """Validate that portfolio meets currency distribution constraints"""
    base_counts = {}
    quote_counts = {}

    for pair in pairs:
        base = get_currency_from_pair(pair, 'base')
        quote = get_currency_from_pair(pair, 'quote')

        base_counts[base] = base_counts.get(base, 0) + 1
        quote_counts[quote] = quote_counts.get(quote, 0) + 1

    # Check constraints
    for currency, count in base_counts.items():
        if count > MAX_CURRENCY_OCCURRENCE:
            return False, f"Base currency {currency} appears {count} times (max {MAX_CURRENCY_OCCURRENCE})"

    for currency, count in quote_counts.items():
        if count > MAX_CURRENCY_OCCURRENCE:
            return False, f"Quote currency {currency} appears {count} times (max {MAX_CURRENCY_OCCURRENCE})"

    return True, "Portfolio distribution is valid"

def get_pair_color(pair, log_return_value):
    """
    Get color for a currency pair based on log returns

    Args:
        pair: Currency pair (e.g., 'EURUSD')
        log_return_value: Log return value for the pair

    Returns:
        Color hex code based on which currency is performing better
    """
    if len(pair) != 6:
        return CURRENCY_COLORS.get('USD', '#2ca02c')  # Default to USD green

    base_currency = pair[:3]
    quote_currency = pair[3:6]

    # Positive log returns mean base currency is strengthening vs quote
    # Negative log returns mean quote currency is strengthening vs base
    if log_return_value >= 0:
        # Base currency is stronger, use base currency color
        return CURRENCY_COLORS.get(base_currency, CURRENCY_COLORS.get('USD', '#2ca02c'))
    else:
        # Quote currency is stronger, use quote currency color
        return CURRENCY_COLORS.get(quote_currency, CURRENCY_COLORS.get('USD', '#2ca02c'))
