import os
import sys
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import numpy as np
import pandas as pd
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import configuration and utilities
from config import MARKET_TIMEZONE, CURRENCY_PAIRS, TIMEFRAME, MAX_RETRIES, RETRY_DELAY
from weekend_utils import is_weekend, get_weekend_time_range, should_use_friday_data, log_weekend_status

# Import MetaTrader5 for direct connection
import MetaTrader5 as mt5
import pytz

# Import Rust library
try:
    from cluster_core import FxPriceData, calculate_log_returns, compute_correlation_matrix
except ImportError as e:
    logger.error(f"Failed to import cluster_core: {e}")
    raise ImportError("cluster_core module not found. Ensure the Rust library is built and installed.")


class MT5Connector:
    """
    MetaTrader 5 connection and data management class for clustering application.
    Self-contained MT5 connector following matrix_QP patterns.
    """

    def __init__(self):
        """Initialize MT5 connector"""
        self.connected = False
        self.last_connection_time = None

    def connect(self, retries: int = MAX_RETRIES) -> bool:
        """
        Connect to MetaTrader 5 terminal

        Args:
            retries: Number of connection attempts

        Returns:
            bool: True if connection successful, False otherwise
        """
        for attempt in range(retries):
            try:
                if mt5.initialize(path="E:\\icdemomt5\\terminal64.exe", portable=True):
                    self.connected = True
                    self.last_connection_time = datetime.now(MARKET_TIMEZONE)
                    logger.info("Successfully connected to MetaTrader 5")

                    # Log account information
                    account_info = mt5.account_info()
                    if account_info:
                        logger.info(f"Connected to account: {account_info.login}")
                        logger.info(f"Server: {account_info.server}")
                        logger.info(f"Currency: {account_info.currency}")

                    return True
                else:
                    error_code = mt5.last_error()
                    logger.warning(f"MT5 connection failed, error code: {error_code}. Attempt {attempt + 1}/{retries}")

            except Exception as e:
                logger.error(f"Exception during MT5 connection attempt {attempt + 1}: {str(e)}")

            if attempt < retries - 1:
                logger.info(f"Retrying connection in {RETRY_DELAY} seconds...")
                import time
                time.sleep(RETRY_DELAY)

        logger.error("Failed to connect to MetaTrader 5 after all attempts")
        self.connected = False
        return False

    def disconnect(self):
        """Disconnect from MetaTrader 5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            logger.info("Disconnected from MetaTrader 5")

    def is_connected(self) -> bool:
        """Check if MT5 connection is active"""
        if not self.connected:
            return False

        # Test connection with a simple call
        try:
            mt5.terminal_info()
            return True
        except Exception:
            self.connected = False
            return False

    def ensure_connection(self) -> bool:
        """Ensure MT5 connection is active, reconnect if necessary"""
        if self.is_connected():
            return True

        logger.info("MT5 connection lost, attempting to reconnect...")
        return self.connect()

    def fetch_data(self, symbols: List[str], start_time: datetime, end_time: datetime) -> Dict[str, pd.DataFrame]:
        """
        Fetch minute-level data for specified symbols and time range

        Args:
            symbols: List of currency pair symbols
            start_time: Start time (timezone-aware)
            end_time: End time (timezone-aware)

        Returns:
            Dictionary mapping symbols to price DataFrames
        """
        if not self.ensure_connection():
            raise ConnectionError("Cannot establish MT5 connection")

        data = {}
        for symbol in symbols:
            try:
                df = self._fetch_pair_data(symbol, start_time, end_time)
                if df is not None and not df.empty:
                    data[symbol] = df
                else:
                    logger.warning(f"No data received for {symbol}")
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {str(e)}")
                continue

        return data

    def _fetch_pair_data(self, pair: str, start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """
        Fetch data for a single currency pair

        Args:
            pair: Currency pair symbol
            start_time: Start time (timezone-aware)
            end_time: End time (timezone-aware)

        Returns:
            DataFrame with OHLC data or None if failed
        """
        try:
            # MT5 has a quirk: it expects UTC timestamps but returns local time data
            # To get the correct time range, we need to add the timezone offset to our request
            offset_hours = start_time.utcoffset().total_seconds() / 3600
            start_adjusted = start_time + timedelta(hours=offset_hours)
            end_adjusted = end_time + timedelta(hours=offset_hours)

            start_timestamp = int(start_adjusted.astimezone(pytz.UTC).timestamp())
            end_timestamp = int(end_adjusted.astimezone(pytz.UTC).timestamp())

            # Fetch rates from MT5
            rates = mt5.copy_rates_range(pair, TIMEFRAME, start_timestamp, end_timestamp)

            if rates is None or len(rates) == 0:
                logger.warning(f"No rates returned for {pair}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(rates)

            # Convert time column to datetime - treat MT5 time as local time (Europe/Bucharest)
            # MT5 returns local time data, so we localize it to the market timezone
            df['time'] = pd.to_datetime(df['time'], unit='s').dt.tz_localize(MARKET_TIMEZONE)

            # Set time as index
            df.set_index('time', inplace=True)

            # Sort by time to ensure proper order
            df.sort_index(inplace=True)

            # Filter out weekend data for consistency
            df = self._filter_weekend_data(df)

            return df

        except Exception as e:
            logger.error(f"Error in _fetch_pair_data for {pair}: {str(e)}")
            return None

    def _filter_weekend_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Filter out weekend data (Saturday and Sunday)

        Args:
            df: DataFrame with datetime index

        Returns:
            DataFrame with weekend data removed
        """
        if df.empty:
            return df

        # Filter out weekends (Saturday=5, Sunday=6)
        weekday_mask = df.index.weekday < 5
        filtered_df = df[weekday_mask]

        if len(filtered_df) < len(df):
            weekend_count = len(df) - len(filtered_df)
            logger.info(f"Filtered out {weekend_count} weekend data points")

        return filtered_df

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


class ClusteringDataManager:
    """
    Manages data flow between MetaTrader 5 and the Rust clustering engine.
    Handles data fetching, preprocessing, and format conversion.
    """
    
    def __init__(self):
        """Initialize the data manager with MT5 connector"""
        self.mt5_connector = MT5Connector()
        logger.info("Initialized ClusteringDataManager")
    
    def fetch_minute_data(
        self,
        symbols: List[str],
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Optional[Dict[str, pd.DataFrame]]:
        """
        Fetch minute-level price data for specified symbols.
        
        Args:
            symbols: List of currency pair symbols
            start_time: Start time for data fetch (default: last 24 hours)
            end_time: End time for data fetch (default: current time)
            
        Returns:
            Dictionary mapping symbols to price DataFrames or None if fetch fails
        """
        try:
            if not self.mt5_connector.ensure_connection():
                raise ConnectionError("Failed to connect to MetaTrader 5")
            
            # Set default time range if not provided - use timezone-aware datetimes
            if end_time is None:
                if should_use_friday_data():
                    # Weekend mode: use Friday's end time
                    friday_start, friday_end = get_weekend_time_range()
                    end_time = friday_end
                    logger.info(f"Weekend mode: Using Friday end time {end_time}")
                else:
                    # Weekday mode: use current time in market timezone
                    end_time = datetime.now(MARKET_TIMEZONE)

            if start_time is None:
                if should_use_friday_data():
                    # Weekend mode: use Friday's start time
                    friday_start, friday_end = get_weekend_time_range()
                    start_time = friday_start
                    logger.info(f"Weekend mode: Using Friday start time {start_time}")
                else:
                    # Weekday mode: go back 24 hours from end_time
                    start_time = end_time - timedelta(days=1)

            # Ensure both times are timezone-aware
            if start_time.tzinfo is None:
                start_time = MARKET_TIMEZONE.localize(start_time)
            if end_time.tzinfo is None:
                end_time = MARKET_TIMEZONE.localize(end_time)

            # Log weekend status for debugging
            log_weekend_status()
            
            # Fetch data from MT5
            raw_data = self.mt5_connector.fetch_data(
                symbols=symbols,
                start_time=start_time,
                end_time=end_time
            )
            
            if not raw_data:
                logger.error("No data received from MT5")
                return None
                
            logger.info(f"Successfully fetched data for {len(raw_data)} symbols")
            return raw_data
            
        except Exception as e:
            logger.error(f"Error fetching minute data: {str(e)}")
            return None
    
    def process_data_with_rust(
        self,
        market_data: Dict[str, pd.DataFrame]
    ) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Process market data through Rust engine for returns and correlation calculations.
        
        Args:
            market_data: Dictionary mapping symbols to price DataFrames
            
        Returns:
            Tuple of (log_returns, correlation_matrix) or (None, None) if processing fails
        """
        try:
            # Extract aligned price data
            symbols = list(market_data.keys())
            aligned_data = self._align_price_data(market_data)
            
            if aligned_data is None:
                return None, None
            
            # Create FxPriceData structure
            price_data = FxPriceData()
            price_data.prices = aligned_data['prices'].tolist()
            price_data.symbols = symbols
            price_data.timestamps = aligned_data['timestamps'].tolist()
            
            # Calculate log returns
            log_returns = calculate_log_returns(price_data)
            if log_returns is None:
                logger.error("Failed to calculate log returns")
                return None, None
                
            # Compute correlation matrix
            correlation_matrix = compute_correlation_matrix(log_returns)
            if correlation_matrix is None:
                logger.error("Failed to compute correlation matrix")
                return None, None
            
            logger.info("Successfully processed data through Rust engine")
            return np.array(log_returns.returns), np.array(correlation_matrix.correlation)
            
        except Exception as e:
            logger.error(f"Error processing data with Rust: {str(e)}")
            return None, None
    
    def _align_price_data(self, market_data: Dict[str, pd.DataFrame]) -> Optional[Dict]:
        """
        Align price data across all symbols to ensure consistent timestamps.
        
        Args:
            market_data: Dictionary mapping symbols to price DataFrames
            
        Returns:
            Dictionary with aligned prices and timestamps or None if alignment fails
        """
        try:
            # Get common timestamps across all symbols
            common_index = None
            for df in market_data.values():
                if common_index is None:
                    common_index = set(df.index)
                else:
                    common_index &= set(df.index)
            
            if not common_index:
                logger.error("No common timestamps found across symbols")
                return None
            
            # Convert to sorted list
            common_index = sorted(common_index)
            
            # Extract aligned prices and timestamps
            aligned_prices = []
            timestamps = []
            
            for timestamp in common_index:
                for symbol in market_data.keys():
                    price = market_data[symbol].loc[timestamp, 'close']
                    aligned_prices.append(price)
                timestamps.append(int(timestamp.timestamp()))
            
            return {
                'prices': np.array(aligned_prices),
                'timestamps': np.array(timestamps)
            }
            
        except Exception as e:
            logger.error(f"Error aligning price data: {str(e)}")
            return None
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if self.mt5_connector:
            self.mt5_connector.disconnect()
            
    def close(self):
        """Clean up resources"""
        if self.mt5_connector:
            self.mt5_connector.disconnect()