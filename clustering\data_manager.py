import os
import sys
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import numpy as np
import pandas as pd
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import configuration and utilities
from config import MARKET_TIMEZONE, CURRENCY_PAIRS
from weekend_utils import is_weekend, get_weekend_time_range, should_use_friday_data, log_weekend_status

# Import MT5Connector
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'matrix_QP'))
from mt5_connector import MT5Connector

# Import Rust library
try:
    from cluster_core import FxPriceData, calculate_log_returns, compute_correlation_matrix
except ImportError as e:
    logger.error(f"Failed to import cluster_core: {e}")
    raise ImportError("cluster_core module not found. Ensure the Rust library is built and installed.")

class ClusteringDataManager:
    """
    Manages data flow between MetaTrader 5 and the Rust clustering engine.
    Handles data fetching, preprocessing, and format conversion.
    """
    
    def __init__(self):
        """Initialize the data manager with MT5 connector"""
        self.mt5_connector = MT5Connector()
        logger.info("Initialized ClusteringDataManager")
    
    def fetch_minute_data(
        self,
        symbols: List[str],
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Optional[Dict[str, pd.DataFrame]]:
        """
        Fetch minute-level price data for specified symbols.
        
        Args:
            symbols: List of currency pair symbols
            start_time: Start time for data fetch (default: last 24 hours)
            end_time: End time for data fetch (default: current time)
            
        Returns:
            Dictionary mapping symbols to price DataFrames or None if fetch fails
        """
        try:
            if not self.mt5_connector.ensure_connection():
                raise ConnectionError("Failed to connect to MetaTrader 5")
            
            # Set default time range if not provided - use timezone-aware datetimes
            if end_time is None:
                if should_use_friday_data():
                    # Weekend mode: use Friday's end time
                    friday_start, friday_end = get_weekend_time_range()
                    end_time = friday_end
                    logger.info(f"Weekend mode: Using Friday end time {end_time}")
                else:
                    # Weekday mode: use current time in market timezone
                    end_time = datetime.now(MARKET_TIMEZONE)

            if start_time is None:
                if should_use_friday_data():
                    # Weekend mode: use Friday's start time
                    friday_start, friday_end = get_weekend_time_range()
                    start_time = friday_start
                    logger.info(f"Weekend mode: Using Friday start time {start_time}")
                else:
                    # Weekday mode: go back 24 hours from end_time
                    start_time = end_time - timedelta(days=1)

            # Ensure both times are timezone-aware
            if start_time.tzinfo is None:
                start_time = MARKET_TIMEZONE.localize(start_time)
            if end_time.tzinfo is None:
                end_time = MARKET_TIMEZONE.localize(end_time)

            # Log weekend status for debugging
            log_weekend_status()
            
            # Fetch data from MT5
            raw_data = self.mt5_connector.fetch_daily_data(
                pairs=symbols,
                start_time=start_time,
                current_day_only=False
            )
            
            if not raw_data:
                logger.error("No data received from MT5")
                return None
                
            logger.info(f"Successfully fetched data for {len(raw_data)} symbols")
            return raw_data
            
        except Exception as e:
            logger.error(f"Error fetching minute data: {str(e)}")
            return None
    
    def process_data_with_rust(
        self,
        market_data: Dict[str, pd.DataFrame]
    ) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Process market data through Rust engine for returns and correlation calculations.
        
        Args:
            market_data: Dictionary mapping symbols to price DataFrames
            
        Returns:
            Tuple of (log_returns, correlation_matrix) or (None, None) if processing fails
        """
        try:
            # Extract aligned price data
            symbols = list(market_data.keys())
            aligned_data = self._align_price_data(market_data)
            
            if aligned_data is None:
                return None, None
            
            # Create FxPriceData structure
            price_data = FxPriceData()
            price_data.prices = aligned_data['prices'].tolist()
            price_data.symbols = symbols
            price_data.timestamps = aligned_data['timestamps'].tolist()
            
            # Calculate log returns
            log_returns = calculate_log_returns(price_data)
            if log_returns is None:
                logger.error("Failed to calculate log returns")
                return None, None
                
            # Compute correlation matrix
            correlation_matrix = compute_correlation_matrix(log_returns)
            if correlation_matrix is None:
                logger.error("Failed to compute correlation matrix")
                return None, None
            
            logger.info("Successfully processed data through Rust engine")
            return np.array(log_returns.returns), np.array(correlation_matrix.correlation)
            
        except Exception as e:
            logger.error(f"Error processing data with Rust: {str(e)}")
            return None, None
    
    def _align_price_data(self, market_data: Dict[str, pd.DataFrame]) -> Optional[Dict]:
        """
        Align price data across all symbols to ensure consistent timestamps.
        
        Args:
            market_data: Dictionary mapping symbols to price DataFrames
            
        Returns:
            Dictionary with aligned prices and timestamps or None if alignment fails
        """
        try:
            # Get common timestamps across all symbols
            common_index = None
            for df in market_data.values():
                if common_index is None:
                    common_index = set(df.index)
                else:
                    common_index &= set(df.index)
            
            if not common_index:
                logger.error("No common timestamps found across symbols")
                return None
            
            # Convert to sorted list
            common_index = sorted(common_index)
            
            # Extract aligned prices and timestamps
            aligned_prices = []
            timestamps = []
            
            for timestamp in common_index:
                for symbol in market_data.keys():
                    price = market_data[symbol].loc[timestamp, 'close']
                    aligned_prices.append(price)
                timestamps.append(int(timestamp.timestamp()))
            
            return {
                'prices': np.array(aligned_prices),
                'timestamps': np.array(timestamps)
            }
            
        except Exception as e:
            logger.error(f"Error aligning price data: {str(e)}")
            return None
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if self.mt5_connector:
            self.mt5_connector.disconnect()
            
    def close(self):
        """Clean up resources"""
        if self.mt5_connector:
            self.mt5_connector.disconnect()