import os
import sys
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import numpy as np
import pandas as pd
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import configuration and utilities
from config import MARKET_TIMEZONE, CURRENCY_PAIRS, TIMEFRAME, MAX_RETRIES, RETRY_DELAY
from weekend_utils import is_weekend, get_weekend_time_range, should_use_friday_data, log_weekend_status, get_available_friday_data_range, get_recent_trading_day_range, get_extended_friday_range

# Import MetaTrader5 for direct connection
import MetaTrader5 as mt5
import pytz

# Import Rust library
try:
    from cluster_core import FxPriceData, calculate_log_returns, compute_correlation_matrix
except ImportError as e:
    logger.error(f"Failed to import cluster_core: {e}")
    raise ImportError("cluster_core module not found. Ensure the Rust library is built and installed.")


class MT5Connector:
    """
    MetaTrader 5 connection and data management class for clustering application.
    Self-contained MT5 connector following matrix_QP patterns.
    """

    def __init__(self):
        """Initialize MT5 connector"""
        self.connected = False
        self.last_connection_time = None

    def connect(self, retries: int = MAX_RETRIES) -> bool:
        """
        Connect to MetaTrader 5 terminal

        Args:
            retries: Number of connection attempts

        Returns:
            bool: True if connection successful, False otherwise
        """
        for attempt in range(retries):
            try:
                if mt5.initialize(path="E:\\icdemomt5\\terminal64.exe", portable=True):
                    self.connected = True
                    self.last_connection_time = datetime.now(MARKET_TIMEZONE)
                    logger.info("Successfully connected to MetaTrader 5")

                    # Log account information
                    account_info = mt5.account_info()
                    if account_info:
                        logger.info(f"Connected to account: {account_info.login}")
                        logger.info(f"Server: {account_info.server}")
                        logger.info(f"Currency: {account_info.currency}")

                    return True
                else:
                    error_code = mt5.last_error()
                    logger.warning(f"MT5 connection failed, error code: {error_code}. Attempt {attempt + 1}/{retries}")

            except Exception as e:
                logger.error(f"Exception during MT5 connection attempt {attempt + 1}: {str(e)}")

            if attempt < retries - 1:
                logger.info(f"Retrying connection in {RETRY_DELAY} seconds...")
                import time
                time.sleep(RETRY_DELAY)

        logger.error("Failed to connect to MetaTrader 5 after all attempts")
        self.connected = False
        return False

    def disconnect(self):
        """Disconnect from MetaTrader 5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            logger.info("Disconnected from MetaTrader 5")

    def is_connected(self) -> bool:
        """Check if MT5 connection is active"""
        if not self.connected:
            return False

        # Test connection with a simple call
        try:
            mt5.terminal_info()
            return True
        except Exception:
            self.connected = False
            return False

    def ensure_connection(self) -> bool:
        """Ensure MT5 connection is active, reconnect if necessary"""
        if self.is_connected():
            return True

        logger.info("MT5 connection lost, attempting to reconnect...")
        return self.connect()

    def fetch_data(self, symbols: List[str], start_time: datetime, end_time: datetime) -> Dict[str, pd.DataFrame]:
        """
        Fetch minute-level data for specified symbols and time range

        Args:
            symbols: List of currency pair symbols
            start_time: Start time (timezone-aware)
            end_time: End time (timezone-aware)

        Returns:
            Dictionary mapping symbols to price DataFrames
        """
        if not self.ensure_connection():
            raise ConnectionError("Cannot establish MT5 connection")

        data = {}
        for symbol in symbols:
            try:
                df = self._fetch_pair_data(symbol, start_time, end_time)
                if df is not None and not df.empty:
                    data[symbol] = df
                else:
                    logger.warning(f"No data received for {symbol}")
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {str(e)}")
                continue

        return data

    def _fetch_pair_data(self, pair: str, start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """
        Fetch data for a single currency pair

        Args:
            pair: Currency pair symbol
            start_time: Start time (timezone-aware)
            end_time: End time (timezone-aware)

        Returns:
            DataFrame with OHLC data or None if failed
        """
        try:
            # Convert timezone-aware datetimes to UTC timestamps for MT5
            # MT5 expects UTC timestamps and returns data in server time
            start_utc = start_time.astimezone(pytz.UTC)
            end_utc = end_time.astimezone(pytz.UTC)

            start_timestamp = int(start_utc.timestamp())
            end_timestamp = int(end_utc.timestamp())

            logger.debug(f"Fetching {pair} data from {start_utc} to {end_utc}")
            logger.debug(f"Timestamps: {start_timestamp} to {end_timestamp}")

            # Try different approaches for historical data
            rates = None

            # Method 1: copy_rates_range (preferred for date ranges)
            rates = mt5.copy_rates_range(pair, TIMEFRAME, start_timestamp, end_timestamp)

            # Method 2: If no data, try copy_rates_from (for historical data)
            if rates is None or len(rates) == 0:
                # Calculate number of bars needed (roughly 1 day = 1440 minutes)
                time_diff = end_timestamp - start_timestamp
                bars_needed = min(time_diff // 60, 2000)  # Limit to 2000 bars max
                logger.debug(f"Trying copy_rates_from with {bars_needed} bars")
                rates = mt5.copy_rates_from(pair, TIMEFRAME, start_timestamp, bars_needed)

            if rates is None or len(rates) == 0:
                logger.warning(f"No rates returned for {pair} between {start_time} and {end_time}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(rates)
            logger.debug(f"Retrieved {len(df)} data points for {pair}")

            # Convert time column to datetime
            # MT5 returns timestamps in server time (usually UTC+2/UTC+3 for European brokers)
            df['time'] = pd.to_datetime(df['time'], unit='s')

            # Localize to server timezone first, then convert to market timezone
            # Most MT5 servers use UTC+2 (EET) or UTC+3 (EEST)
            server_tz = pytz.timezone('Europe/Bucharest')  # EET/EEST
            df['time'] = df['time'].dt.tz_localize(server_tz, ambiguous='infer')
            df['time'] = df['time'].dt.tz_convert(MARKET_TIMEZONE)

            # Set time as index
            df.set_index('time', inplace=True)

            # Sort by time to ensure proper order
            df.sort_index(inplace=True)

            # For weekend requests, don't filter weekend data - we want to keep it
            if not should_use_friday_data():
                df = self._filter_weekend_data(df)

            logger.info(f"Successfully fetched {len(df)} data points for {pair}")
            return df

        except Exception as e:
            logger.error(f"Error in _fetch_pair_data for {pair}: {str(e)}")
            return None

    def _filter_weekend_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Filter out weekend data (Saturday and Sunday)

        Args:
            df: DataFrame with datetime index

        Returns:
            DataFrame with weekend data removed
        """
        if df.empty:
            return df

        # Filter out weekends (Saturday=5, Sunday=6)
        weekday_mask = df.index.weekday < 5
        filtered_df = df[weekday_mask]

        if len(filtered_df) < len(df):
            weekend_count = len(df) - len(filtered_df)
            logger.info(f"Filtered out {weekend_count} weekend data points")

        return filtered_df

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


class ClusteringDataManager:
    """
    Manages data flow between MetaTrader 5 and the Rust clustering engine.
    Handles data fetching, preprocessing, and format conversion.
    """
    
    def __init__(self):
        """Initialize the data manager with MT5 connector"""
        self.mt5_connector = MT5Connector()
        logger.info("Initialized ClusteringDataManager")
    
    def fetch_minute_data(
        self,
        symbols: List[str],
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Optional[Dict[str, pd.DataFrame]]:
        """
        Fetch minute-level price data for specified symbols.
        
        Args:
            symbols: List of currency pair symbols
            start_time: Start time for data fetch (default: last 24 hours)
            end_time: End time for data fetch (default: current time)
            
        Returns:
            Dictionary mapping symbols to price DataFrames or None if fetch fails
        """
        try:
            if not self.mt5_connector.ensure_connection():
                raise ConnectionError("Failed to connect to MetaTrader 5")
            
            # Set default time range if not provided - use timezone-aware datetimes
            if end_time is None:
                if should_use_friday_data():
                    # Weekend mode: try to find Friday with available data
                    friday_start, friday_end, weeks_back = get_available_friday_data_range()
                    end_time = friday_end
                    logger.info(f"Weekend mode: Using Friday end time {end_time} (weeks back: {weeks_back})")
                else:
                    # Weekday mode: use current time in market timezone
                    end_time = datetime.now(MARKET_TIMEZONE)

            if start_time is None:
                if should_use_friday_data():
                    # Weekend mode: use Friday's start time (matching the end_time calculation)
                    friday_start, friday_end, weeks_back = get_available_friday_data_range()
                    start_time = friday_start
                    logger.info(f"Weekend mode: Using Friday start time {start_time} (weeks back: {weeks_back})")
                else:
                    # Weekday mode: go back 24 hours from end_time
                    start_time = end_time - timedelta(days=1)

            # Ensure both times are timezone-aware
            if start_time.tzinfo is None:
                start_time = MARKET_TIMEZONE.localize(start_time)
            if end_time.tzinfo is None:
                end_time = MARKET_TIMEZONE.localize(end_time)

            # Log weekend status for debugging
            log_weekend_status()
            
            # Fetch data from MT5 with fallback for weekends
            raw_data = self.mt5_connector.fetch_data(
                symbols=symbols,
                start_time=start_time,
                end_time=end_time
            )

            # If no data and it's weekend, try previous Fridays and recent trading days
            if not raw_data and should_use_friday_data():
                logger.warning("No data for current Friday, trying previous Fridays with extended ranges...")

                # Try extended Friday ranges (Thursday evening to Friday evening)
                for weeks_back in range(0, 8):  # Try up to 8 weeks back
                    try:
                        friday_start, friday_end = get_extended_friday_range(weeks_back)
                        logger.info(f"Trying extended Friday range {friday_start.strftime('%Y-%m-%d %H:%M')} to {friday_end.strftime('%Y-%m-%d %H:%M')} (weeks back: {weeks_back})")

                        raw_data = self.mt5_connector.fetch_data(
                            symbols=symbols,
                            start_time=friday_start,
                            end_time=friday_end
                        )

                        if raw_data:
                            logger.info(f"Successfully found data for extended Friday range (weeks back: {weeks_back})")
                            break
                    except Exception as e:
                        logger.warning(f"Failed to fetch extended Friday data for weeks_back={weeks_back}: {e}")
                        continue

                # If still no data, try recent trading days with broader range
                if not raw_data:
                    logger.warning("No Friday data found, trying recent trading days...")
                    for days_back in range(1, 30):  # Try up to 30 days back
                        try:
                            now = datetime.now(MARKET_TIMEZONE)
                            target_date = now - timedelta(days=days_back)

                            # Only try weekdays
                            if target_date.weekday() < 5:
                                trading_start = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
                                trading_end = target_date.replace(hour=23, minute=59, second=59, microsecond=0)

                                logger.info(f"Trying trading day: {target_date.strftime('%Y-%m-%d')} (days back: {days_back})")

                                raw_data = self.mt5_connector.fetch_data(
                                    symbols=symbols,
                                    start_time=trading_start,
                                    end_time=trading_end
                                )

                                if raw_data:
                                    logger.info(f"Successfully found data for trading day {target_date.strftime('%Y-%m-%d')}")
                                    break
                        except Exception as e:
                            logger.warning(f"Failed to fetch trading day data for days_back={days_back}: {e}")
                            continue

            if not raw_data:
                if should_use_friday_data():
                    logger.warning("No historical data available from MT5 demo server, generating mock Friday data for weekend display")
                    raw_data = self._generate_mock_weekend_data(symbols)
                else:
                    logger.error("No data received from MT5 after trying multiple Fridays")
                    return None
                
            logger.info(f"Successfully fetched data for {len(raw_data)} symbols")
            return raw_data
            
        except Exception as e:
            logger.error(f"Error fetching minute data: {str(e)}")
            return None

    def _generate_mock_weekend_data(self, symbols: List[str]) -> Dict[str, pd.DataFrame]:
        """
        Generate mock market data for weekend display when no historical data is available.

        Args:
            symbols: List of currency pair symbols

        Returns:
            Dictionary mapping symbols to mock price DataFrames
        """
        import numpy as np

        # Mock base prices for major currency pairs
        base_prices = {
            'EUR/USD': 1.0850,
            'GBP/USD': 1.2650,
            'USD/JPY': 149.50,
            'USD/CHF': 0.8750,
            'USD/CAD': 1.3580,
            'AUD/USD': 0.6720,
            'NZD/USD': 0.6150,
            'EUR/GBP': 0.8580,
            'EUR/JPY': 162.20,
            'GBP/JPY': 189.10
        }

        mock_data = {}
        friday_start, friday_end, _ = get_available_friday_data_range()

        # Generate 24 hours of minute data (1440 minutes)
        minutes_in_day = 24 * 60
        timestamps = pd.date_range(
            start=friday_start,
            end=friday_end,
            freq='1min',
            tz=MARKET_TIMEZONE
        )[:minutes_in_day]  # Limit to exactly 1440 minutes

        for symbol in symbols:
            base_price = base_prices.get(symbol, 1.0000)

            # Generate realistic price movements
            np.random.seed(42)  # For reproducible mock data
            returns = np.random.normal(0, 0.0001, len(timestamps))  # Small random movements
            prices = base_price * np.exp(np.cumsum(returns))

            # Create OHLC data
            df_data = []
            for i, (timestamp, price) in enumerate(zip(timestamps, prices)):
                # Add small random variations for OHLC
                variation = np.random.normal(0, 0.00005)
                open_price = price + variation
                high_price = price + abs(variation) + np.random.uniform(0, 0.0001)
                low_price = price - abs(variation) - np.random.uniform(0, 0.0001)
                close_price = price

                df_data.append({
                    'time': timestamp,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'tick_volume': np.random.randint(50, 200),
                    'spread': np.random.randint(1, 5),
                    'real_volume': 0
                })

            df = pd.DataFrame(df_data)
            df.set_index('time', inplace=True)
            mock_data[symbol] = df

            logger.info(f"Generated mock data for {symbol}: {len(df)} data points")

        logger.info(f"Generated mock weekend data for {len(mock_data)} symbols")
        return mock_data
    
    def process_data_with_rust(
        self,
        market_data: Dict[str, pd.DataFrame]
    ) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Process market data through Rust engine for returns and correlation calculations.
        
        Args:
            market_data: Dictionary mapping symbols to price DataFrames
            
        Returns:
            Tuple of (log_returns, correlation_matrix) or (None, None) if processing fails
        """
        try:
            # Extract aligned price data
            symbols = list(market_data.keys())
            aligned_data = self._align_price_data(market_data)
            
            if aligned_data is None:
                return None, None
            
            # Create FxPriceData structure
            price_data = FxPriceData()
            price_data.prices = aligned_data['prices'].tolist()
            price_data.symbols = symbols
            price_data.timestamps = aligned_data['timestamps'].tolist()
            
            # Calculate log returns
            log_returns = calculate_log_returns(price_data)
            if log_returns is None:
                logger.error("Failed to calculate log returns")
                return None, None
                
            # Compute correlation matrix
            correlation_matrix = compute_correlation_matrix(log_returns)
            if correlation_matrix is None:
                logger.error("Failed to compute correlation matrix")
                return None, None
            
            logger.info("Successfully processed data through Rust engine")
            return np.array(log_returns.returns), np.array(correlation_matrix.correlation)
            
        except Exception as e:
            logger.error(f"Error processing data with Rust: {str(e)}")
            return None, None
    
    def _align_price_data(self, market_data: Dict[str, pd.DataFrame]) -> Optional[Dict]:
        """
        Align price data across all symbols to ensure consistent timestamps.
        
        Args:
            market_data: Dictionary mapping symbols to price DataFrames
            
        Returns:
            Dictionary with aligned prices and timestamps or None if alignment fails
        """
        try:
            # Get common timestamps across all symbols
            common_index = None
            for df in market_data.values():
                if common_index is None:
                    common_index = set(df.index)
                else:
                    common_index &= set(df.index)
            
            if not common_index:
                logger.error("No common timestamps found across symbols")
                return None
            
            # Convert to sorted list
            common_index = sorted(common_index)
            
            # Extract aligned prices and timestamps
            aligned_prices = []
            timestamps = []
            
            for timestamp in common_index:
                for symbol in market_data.keys():
                    price = market_data[symbol].loc[timestamp, 'close']
                    aligned_prices.append(price)
                timestamps.append(int(timestamp.timestamp()))
            
            return {
                'prices': np.array(aligned_prices),
                'timestamps': np.array(timestamps)
            }
            
        except Exception as e:
            logger.error(f"Error aligning price data: {str(e)}")
            return None
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if self.mt5_connector:
            self.mt5_connector.disconnect()
            
    def close(self):
        """Clean up resources"""
        if self.mt5_connector:
            self.mt5_connector.disconnect()