#!/usr/bin/env python3
"""
Test script for linear regression channel functionality in Matrix_QP dashboard
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dashboard import PortfolioDashboard

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_linear_regression_channel():
    """Test the linear regression channel calculation"""
    logger.info("Testing linear regression channel functionality...")
    
    # Create a sample cumulative returns series
    np.random.seed(42)  # For reproducible results
    dates = pd.date_range(start='2024-01-01', periods=200, freq='H')
    
    # Create synthetic cumulative returns with trend and noise
    trend = np.linspace(0, 0.05, 200)  # 5% upward trend
    noise = np.random.normal(0, 0.01, 200)  # 1% volatility
    cumulative_returns = trend + noise.cumsum() * 0.1
    
    returns_series = pd.Series(cumulative_returns, index=dates)
    
    # Create dashboard instance
    dashboard = PortfolioDashboard()
    
    # Test the linear regression channel calculation
    regression_data = dashboard._calculate_linear_regression_channel(returns_series, length=120)
    
    # Verify the results
    assert 'mid' in regression_data, "Missing 'mid' in regression data"
    assert 'high_1sd' in regression_data, "Missing 'high_1sd' in regression data"
    assert 'low_1sd' in regression_data, "Missing 'low_1sd' in regression data"
    assert 'high_2sd' in regression_data, "Missing 'high_2sd' in regression data"
    assert 'low_2sd' in regression_data, "Missing 'low_2sd' in regression data"
    assert 'x_values' in regression_data, "Missing 'x_values' in regression data"
    assert 'slope' in regression_data, "Missing 'slope' in regression data"
    assert 'r_squared' in regression_data, "Missing 'r_squared' in regression data"
    
    # Check that we have the right number of points (120)
    assert len(regression_data['mid']) == 120, f"Expected 120 points, got {len(regression_data['mid'])}"
    assert len(regression_data['x_values']) == 120, f"Expected 120 x_values, got {len(regression_data['x_values'])}"
    
    # Check that 2SD lines are wider than 1SD lines
    mid_values = regression_data['mid']
    high_1sd = regression_data['high_1sd']
    high_2sd = regression_data['high_2sd']
    low_1sd = regression_data['low_1sd']
    low_2sd = regression_data['low_2sd']
    
    # Verify that 2SD is further from mid than 1SD
    assert np.all(high_2sd >= high_1sd), "2SD high line should be >= 1SD high line"
    assert np.all(low_2sd <= low_1sd), "2SD low line should be <= 1SD low line"
    assert np.all(high_1sd >= mid_values), "1SD high line should be >= mid line"
    assert np.all(low_1sd <= mid_values), "1SD low line should be <= mid line"
    
    # Check that R-squared is between 0 and 1
    assert 0 <= regression_data['r_squared'] <= 1, f"R-squared should be between 0 and 1, got {regression_data['r_squared']}"
    
    logger.info(f"✅ Linear regression channel test passed!")
    logger.info(f"   - Slope: {regression_data['slope']:.6f}")
    logger.info(f"   - R²: {regression_data['r_squared']:.4f}")
    logger.info(f"   - Channel width (1SD): {np.mean(high_1sd - low_1sd):.6f}")
    logger.info(f"   - Channel width (2SD): {np.mean(high_2sd - low_2sd):.6f}")
    
    return True

def test_edge_cases():
    """Test edge cases for linear regression channel"""
    logger.info("Testing edge cases...")
    
    dashboard = PortfolioDashboard()
    
    # Test with insufficient data
    short_series = pd.Series([1, 2, 3], index=pd.date_range('2024-01-01', periods=3, freq='H'))
    result = dashboard._calculate_linear_regression_channel(short_series, length=120)
    
    # Should handle short series gracefully
    assert len(result['mid']) == 3, "Should use available data length when insufficient"
    
    # Test with very short series (< 2 points)
    very_short_series = pd.Series([1], index=pd.date_range('2024-01-01', periods=1, freq='H'))
    result = dashboard._calculate_linear_regression_channel(very_short_series, length=120)
    
    # Should return empty arrays
    assert len(result['mid']) == 0, "Should return empty arrays for insufficient data"
    
    logger.info("✅ Edge cases test passed!")
    
    return True

if __name__ == "__main__":
    try:
        logger.info("🚀 Starting Linear Regression Channel Tests")
        logger.info("=" * 50)
        
        # Run tests
        test_linear_regression_channel()
        test_edge_cases()
        
        logger.info("=" * 50)
        logger.info("🎉 All tests passed! Linear regression channel is working correctly.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        sys.exit(1)
