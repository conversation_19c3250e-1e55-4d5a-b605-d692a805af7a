#!/usr/bin/env python3
"""
Test script to verify DatetimeIndex ambiguity fix for Combined Absolute Cumulative Returns charts
in the matrix_QP - Tickmill project.

This script tests chart creation for all time periods (24h, 48h, 72h, 120h) to ensure
the DatetimeIndex ambiguity error has been resolved.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_multi_day_data():
    """Create sample market data spanning multiple days for testing"""
    logger.info("Creating sample multi-day market data...")
    
    # Create 5 days of hourly data (120 hours)
    start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=4)
    time_range = pd.date_range(start=start_time, periods=120, freq='H')
    
    # Filter out weekends (Saturday=5, Sunday=6)
    weekday_mask = time_range.weekday < 5
    time_range = time_range[weekday_mask]
    
    # Create sample currency pairs
    pairs = ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDJPY']
    
    # Generate realistic price movements
    np.random.seed(42)  # For reproducible results
    data = {}
    
    for pair in pairs:
        # Start with a base price
        base_price = 1.0 + np.random.normal(0, 0.2)
        
        # Generate price movements with some trend and volatility
        returns = np.random.normal(0, 0.001, len(time_range))  # Small hourly returns
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        data[pair] = prices
    
    # Create DataFrame
    market_data = pd.DataFrame(data, index=time_range)
    
    logger.info(f"Created sample data: {len(market_data)} data points from {market_data.index.min()} to {market_data.index.max()}")
    return market_data

def test_chart_creation_for_period(dashboard, time_period):
    """Test chart creation for a specific time period"""
    logger.info(f"Testing chart creation for time period: {time_period}")
    
    try:
        # Create a sample portfolio
        sample_portfolio = {
            'strategy': 'max_sharpe',
            'pairs': ['EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDJPY'],
            'weights': [0.2, 0.15, 0.15, 0.15, 0.15, 0.2],
            'expected_return': 0.05,
            'volatility': 0.12,
            'sharpe_ratio': 0.42
        }
        
        # Test chart creation
        chart_figure = dashboard._create_portfolio_returns_chart(sample_portfolio, time_period)
        
        # Verify chart was created successfully
        if chart_figure and hasattr(chart_figure, 'data'):
            trace_count = len(chart_figure.data)
            logger.info(f"✓ Chart created successfully for {time_period} with {trace_count} traces")
            return True
        else:
            logger.error(f"✗ Chart creation failed for {time_period} - no figure data")
            return False
            
    except Exception as e:
        logger.error(f"✗ Chart creation failed for {time_period}: {str(e)}")
        return False

def main():
    """Main test function"""
    logger.info("Starting DatetimeIndex ambiguity fix test for matrix_QP - Tickmill")
    
    try:
        # Import dashboard components
        from dashboard import PortfolioDashboard
        from mt5_connector import MT5Connector
        from log_returns import LogReturnsCalculator
        
        # Create dashboard instance
        logger.info("Creating dashboard instance...")
        dashboard = PortfolioDashboard()
        
        # Create sample market data
        sample_data = create_sample_multi_day_data()
        
        # Mock the returns data for testing
        dashboard.current_returns = dashboard.returns_calculator.calculate_log_returns(sample_data)
        
        # Test all time periods
        time_periods = ['24h', '48h', '72h', '120h']
        results = {}
        
        for period in time_periods:
            success = test_chart_creation_for_period(dashboard, period)
            results[period] = success
        
        # Report results
        logger.info("\n" + "="*50)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("="*50)
        
        all_passed = True
        for period, success in results.items():
            status = "PASS" if success else "FAIL"
            logger.info(f"{period:>6}: {status}")
            if not success:
                all_passed = False
        
        logger.info("="*50)
        
        if all_passed:
            logger.info("✓ ALL TESTS PASSED - DatetimeIndex ambiguity fix is working correctly!")
            return 0
        else:
            logger.error("✗ SOME TESTS FAILED - DatetimeIndex ambiguity issue may still exist")
            return 1
            
    except Exception as e:
        logger.error(f"Test setup failed: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
