[package]
name = "cluster_core"
version = "0.1.0"
edition = "2021"

[lib]
name = "cluster_core"
crate-type = ["cdylib"]

[dependencies]
pyo3 = { version = "0.19", features = ["extension-module"] }
ndarray = "0.15"
numpy = "0.19"
chrono = "0.4"
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
log = "0.4"
env_logger = "0.10"
linfa = "0.7"
linfa-clustering = "0.7"
ndarray-linalg = "0.16"
rand = "0.8"
rand_isaac = "0.3"

[dev-dependencies]
criterion = "0.5"
