/* Main container styling */
.container-fluid {
    max-width: 1800px;
    margin: 0 auto;
}

/* Card styling */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

/* Graph container styling */
.dash-graph {
    height: 400px;
}

/* Stats panel styling */
.stats-panel {
    height: 300px;
    overflow-y: auto;
}

/* Events panel styling */
.events-panel {
    height: 300px;
    overflow-y: auto;
}

/* Loading spinner styling */
._dash-loading {
    margin: 3rem auto;
    color: #007bff;
}

/* Time scrubber styling */
.time-controls .card-body {
    padding: 1rem 1.5rem;
}

.time-controls .form-label {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Time slider styling */
.rc-slider {
    margin: 1rem 0;
    height: 14px;
}

.rc-slider-rail {
    background-color: #e9ecef;
    height: 4px;
}

.rc-slider-track {
    background-color: #007bff;
    height: 4px;
}

.rc-slider-handle {
    border: 3px solid #007bff;
    background-color: #fff;
    width: 16px;
    height: 16px;
    margin-top: -6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rc-slider-handle:hover {
    border-color: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.rc-slider-handle:active {
    border-color: #0056b3;
    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.25);
}

/* Slider marks styling */
.rc-slider-mark {
    font-size: 12px;
    color: #6c757d;
}

.rc-slider-mark-text {
    font-size: 11px;
    color: #6c757d;
    white-space: nowrap;
    transform: rotate(-45deg);
    transform-origin: 50% 50%;
}

.rc-slider-mark-text:hover {
    color: #007bff;
    font-weight: 500;
}

/* Time controls button styling */
.time-controls .btn-group {
    width: 100%;
}

.time-controls .btn {
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.time-controls .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.time-controls .btn i {
    font-size: 0.875rem;
}

/* Time display styling */
.time-controls .time-display {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #e9ecef;
}

/* Tooltip styling for slider */
.rc-slider-tooltip {
    font-size: 12px;
    padding: 4px 8px;
    background-color: #333;
    color: white;
    border-radius: 4px;
    white-space: nowrap;
}

.rc-slider-tooltip-arrow {
    border-top-color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dash-graph {
        height: 300px;
    }
    
    .stats-panel,
    .events-panel {
        height: 200px;
    }
}

/* Event log specific styling */
.events-panel .table {
    font-size: 0.9rem;
}

.events-panel .table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-top: none;
}

.events-panel .table td {
    vertical-align: middle;
}

.events-panel .btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

/* Modal styling */
.modal-xl {
    max-width: 95%;
}

.modal-content {
    border-radius: 0.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 1.5rem;
}

/* Event comparison styling */
.event-comparison .card {
    border: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.event-comparison .card-header {
    font-weight: 600;
}

.event-comparison .text-info {
    color: #17a2b8 !important;
}

.event-comparison .text-success {
    color: #28a745 !important;
}

/* Badge styling */
.badge {
    font-size: 0.8em;
    padding: 0.4em 0.6em;
}

/* Hover effects */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
}

/* Responsive modal adjustments */
@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 0.5rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .events-panel .table {
        font-size: 0.8rem;
    }
    
    .events-panel .btn-sm {
        padding: 0.2rem 0.5rem;
        font-size: 0.7rem;
    }
}

/* ============================================
   REGIME CALENDAR STYLING
   ============================================ */

/* Tab styling */
.custom-tab {
    font-weight: 500;
    color: #495057;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem 0.375rem 0 0;
    margin-right: 0.25rem;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
}

.custom-tab:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.custom-tab.tab--selected {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    font-weight: 600;
}

/* Regime calendar container */
.regime-calendar-panel {
    min-height: 400px;
    padding: 1rem;
}

/* Regime calendar cells */
.regime-calendar-cell {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 60px;
    font-size: 0.875rem;
    position: relative;
}

.regime-calendar-cell:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
    z-index: 10;
}

.regime-calendar-cell:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Regime legend styling */
.regime-legend-panel {
    padding: 0.5rem;
}

.regime-legend-color {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.regime-legend-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.regime-legend-item:hover {
    background-color: #e9ecef;
    transform: translateX(4px);
}

/* Regime statistics styling */
.regime-stats-panel {
    padding: 0.5rem;
}

.regime-stats-panel .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.regime-stats-panel .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem;
    font-weight: 600;
}

.regime-stats-panel .card-body {
    padding: 0.75rem;
}

/* Regime controls styling */
.regime-controls-panel {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.regime-controls-panel .form-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.regime-controls-panel .form-control,
.regime-controls-panel .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.regime-controls-panel .form-control:focus,
.regime-controls-panel .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Regime color scheme classes */
.regime-color-0 { background-color: #1f77b4; } /* Blue - Asian Session */
.regime-color-1 { background-color: #ff7f0e; } /* Orange - European Session */
.regime-color-2 { background-color: #2ca02c; } /* Green - US Session */
.regime-color-3 { background-color: #d62728; } /* Red - High Volatility */
.regime-color-4 { background-color: #9467bd; } /* Purple - Quiet Period */
.regime-color-5 { background-color: #8c564b; } /* Brown - Market Open */
.regime-color-6 { background-color: #e377c2; } /* Pink - Market Close */
.regime-color-7 { background-color: #7f7f7f; } /* Gray - Weekend/Holiday */

/* Regime calendar week headers */
.regime-calendar-header {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    text-align: center;
    color: #495057;
}

/* Regime calendar month navigation */
.regime-calendar-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.regime-calendar-nav .btn {
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Alert styling for regime notifications */
.regime-alert {
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.regime-alert.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.regime-alert.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.regime-alert.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Loading states for regime components */
.regime-loading {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.regime-loading .spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 0.2rem;
}

/* Responsive adjustments for regime components */
@media (max-width: 768px) {
    .regime-calendar-cell {
        min-height: 50px;
        font-size: 0.75rem;
    }
    
    .regime-legend-panel,
    .regime-stats-panel {
        padding: 0.25rem;
    }
    
    .regime-controls-panel {
        padding: 0.75rem;
    }
    
    .custom-tab {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .regime-calendar-cell {
        min-height: 40px;
        font-size: 0.7rem;
    }
    
    .regime-calendar-panel {
        padding: 0.5rem;
    }
    
    .regime-stats-panel .card-body {
        padding: 0.5rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .regime-calendar-cell {
        border-width: 2px;
    }
    
    .regime-legend-color {
        border-width: 2px;
        border-color: #000;
    }
    
    .custom-tab {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .regime-calendar-cell,
    .regime-legend-item,
    .custom-tab {
        transition: none;
    }
    
    .regime-calendar-cell:hover,
    .regime-legend-item:hover {
        transform: none;
    }
}

/* Print styles for regime calendar */
@media print {
    .regime-calendar-cell {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .regime-controls-panel {
        display: none;
    }
    
    .custom-tab {
        display: none;
    }
}