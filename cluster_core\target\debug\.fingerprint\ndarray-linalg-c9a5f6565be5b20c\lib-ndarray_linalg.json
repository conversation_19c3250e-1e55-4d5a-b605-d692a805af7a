{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"default\", \"intel-mkl\", \"intel-mkl-static\", \"intel-mkl-system\", \"netlib\", \"netlib-static\", \"netlib-system\", \"openblas\", \"openblas-static\", \"openblas-system\"]", "target": 13331890148219350196, "profile": 2241668132362809309, "path": 6134431032675170810, "deps": [[514429886033515986, "kate<PERSON>t", false, 7398917580488880808], [3008854931152362171, "n<PERSON><PERSON>", false, 16633511415824908303], [3012893206755459258, "lax", false, 5861071887805369634], [5157631553186200874, "num_traits", false, 8171727555973592493], [5913101255804133335, "cauchy", false, 16717393573439173057], [8008191657135824715, "thiserror", false, 8015957111518636185], [12319020793864570031, "num_complex", false, 8410273256261348971], [13208667028893622512, "rand", false, 15746186270168620571]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ndarray-linalg-c9a5f6565be5b20c\\dep-lib-ndarray_linalg", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}