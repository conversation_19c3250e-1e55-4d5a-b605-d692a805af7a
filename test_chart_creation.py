#!/usr/bin/env python3
"""
Test script for portfolio returns chart creation in Matrix_QP dashboard
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dashboard import PortfolioDashboard

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_portfolio_returns_chart():
    """Test the portfolio returns chart creation with linear regression"""
    logger.info("Testing portfolio returns chart creation...")
    
    # Create a dashboard instance
    dashboard = PortfolioDashboard()
    
    # Create sample portfolio data
    portfolio_data = {
        'pairs': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD'],
        'weights': [0.2, 0.15, -0.1, 0.25, 0.3, 0.2],
        'strategy': 'max_sharpe',
        'metrics': {
            'expected_return': 0.001234,
            'volatility': 0.012345,
            'sharpe_ratio': 1.234
        }
    }
    
    # Create sample returns data
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=500, freq='h')
    
    # Create synthetic returns data for the pairs
    returns_data = {}
    for pair in portfolio_data['pairs']:
        # Create realistic forex returns (small values with some correlation)
        base_trend = np.random.normal(0, 0.0001, 500)
        noise = np.random.normal(0, 0.0005, 500)
        returns_data[pair] = base_trend + noise
    
    # Create DataFrame
    returns_df = pd.DataFrame(returns_data, index=dates)
    
    # Set the current returns in the dashboard
    dashboard.current_returns = returns_df
    
    try:
        # Test chart creation
        logger.info("Creating portfolio returns chart...")
        fig = dashboard._create_portfolio_returns_chart(portfolio_data, time_period='24h')
        
        # Verify the figure was created
        assert fig is not None, "Figure should not be None"
        assert hasattr(fig, 'data'), "Figure should have data attribute"
        assert len(fig.data) > 0, "Figure should have traces"
        
        # Check if we have the expected traces
        trace_names = [trace.name for trace in fig.data if hasattr(trace, 'name')]
        logger.info(f"Chart traces found: {trace_names}")
        
        # Should have portfolio returns trace
        portfolio_traces = [name for name in trace_names if 'Portfolio Returns' in str(name)]
        assert len(portfolio_traces) > 0, "Should have portfolio returns trace"
        
        # Check for linear regression traces
        regression_traces = [name for name in trace_names if 'Regression' in str(name)]
        logger.info(f"Regression traces found: {len(regression_traces)}")
        
        # Should have 5 regression traces (mid, ±1SD, ±2SD)
        expected_regression_traces = ['Regression +2SD', 'Regression +1SD', 'Regression Mid', 'Regression -1SD', 'Regression -2SD']
        found_regression_traces = [name for name in trace_names if name in expected_regression_traces]
        
        if len(found_regression_traces) == 5:
            logger.info("✅ All 5 linear regression channel traces found!")
            for trace_name in found_regression_traces:
                logger.info(f"   - {trace_name}")
        else:
            logger.warning(f"⚠️  Expected 5 regression traces, found {len(found_regression_traces)}: {found_regression_traces}")
        
        # Check if figure has subplots (should have 2: returns and RSI)
        if hasattr(fig, '_grid_ref') and fig._grid_ref:
            subplot_count = len(fig._grid_ref)
            logger.info(f"Subplots found: {subplot_count}")
            assert subplot_count >= 2, "Should have at least 2 subplots (returns and RSI)"
        
        logger.info("✅ Portfolio returns chart creation test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chart creation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_data_handling():
    """Test chart creation with empty data"""
    logger.info("Testing empty data handling...")
    
    dashboard = PortfolioDashboard()
    
    # Set empty returns data
    dashboard.current_returns = pd.DataFrame()
    
    portfolio_data = {
        'pairs': ['EURUSD', 'GBPUSD'],
        'weights': [0.5, 0.5],
        'strategy': 'test'
    }
    
    try:
        fig = dashboard._create_portfolio_returns_chart(portfolio_data, time_period='24h')
        
        # Should return an empty figure with error message
        assert fig is not None, "Should return a figure even with empty data"
        logger.info("✅ Empty data handling test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Empty data handling failed: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        logger.info("🚀 Starting Portfolio Returns Chart Tests")
        logger.info("=" * 60)
        
        # Run tests
        test1_passed = test_portfolio_returns_chart()
        test2_passed = test_empty_data_handling()
        
        logger.info("=" * 60)
        if test1_passed and test2_passed:
            logger.info("🎉 All tests passed! Portfolio returns chart with linear regression is working correctly.")
        else:
            logger.error("❌ Some tests failed!")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ Test suite failed: {str(e)}")
        sys.exit(1)
