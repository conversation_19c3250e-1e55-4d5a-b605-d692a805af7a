{"$message_type":"diagnostic","message":"unused import: `PyArray1`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":80,"byte_end":88,"line_start":3,"line_end":3,"column_start":23,"column_end":31,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":23,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":78,"byte_end":88,"line_start":3,"line_end":3,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":69,"byte_end":70,"line_start":3,"line_end":3,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":88,"byte_end":89,"line_start":3,"line_end":3,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `PyArray1`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:3:23\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse numpy::{PyArray2, PyArray1};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime`, `NaiveDateTime`, and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":255,"byte_end":263,"line_start":9,"line_end":9,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":265,"byte_end":268,"line_start":9,"line_end":9,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":270,"byte_end":283,"line_start":9,"line_end":9,"column_start":29,"column_end":42,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":29,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":255,"byte_end":285,"line_start":9,"line_end":9,"column_start":14,"column_end":44,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":14,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":254,"byte_end":255,"line_start":9,"line_end":9,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":293,"byte_end":294,"line_start":9,"line_end":9,"column_start":52,"column_end":53,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":52,"highlight_end":53}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `DateTime`, `NaiveDateTime`, and `Utc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:9:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc, NaiveDateTime, Timelike};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `KMeansParams`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":350,"byte_end":362,"line_start":11,"line_end":11,"column_start":32,"column_end":44,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":32,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":348,"byte_end":362,"line_start":11,"line_end":11,"column_start":30,"column_end":44,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":30,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":341,"byte_end":342,"line_start":11,"line_end":11,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":362,"byte_end":363,"line_start":11,"line_end":11,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `KMeansParams`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:11:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse linfa_clustering::{KMeans, KMeansParams};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `calculate_hourly_volatility` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":40949,"byte_end":40976,"line_start":1261,"line_end":1261,"column_start":4,"column_end":31,"is_primary":true,"text":[{"text":"fn calculate_hourly_volatility(day_data: &[&Vec<f64>]) -> PyResult<[f64; 24]> {","highlight_start":4,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `calculate_hourly_volatility` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1261:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1261\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn calculate_hourly_volatility(day_data: &[&Vec<f64>]) -> PyResult<[f64; 24]> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1586,"line_start":61,"line_end":61,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":1604,"byte_end":1614,"line_start":62,"line_end":62,"column_start":6,"column_end":16,"is_primary":false,"text":[{"text":"impl LogReturns {","highlight_start":6,"highlight_end":16}],"label":"`LogReturns` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1586,"line_start":61,"line_end":61,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":1604,"byte_end":1614,"line_start":62,"line_end":62,"column_start":6,"column_end":16,"is_primary":false,"text":[{"text":"impl LogReturns {","highlight_start":6,"highlight_end":16}],"label":"`LogReturns` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(non_local_definitions)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:61:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl LogReturns {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`LogReturns` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`LogReturns` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_local_definitions)]` on by default\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2084,"line_start":85,"line_end":85,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2102,"byte_end":2119,"line_start":86,"line_end":86,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl CorrelationMatrix {","highlight_start":6,"highlight_end":23}],"label":"`CorrelationMatrix` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2084,"line_start":85,"line_end":85,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2102,"byte_end":2119,"line_start":86,"line_end":86,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl CorrelationMatrix {","highlight_start":6,"highlight_end":23}],"label":"`CorrelationMatrix` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:85:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CorrelationMatrix {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`CorrelationMatrix` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`CorrelationMatrix` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3126,"line_start":131,"line_end":131,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":3144,"byte_end":3161,"line_start":132,"line_end":132,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl ClusterStatistics {","highlight_start":6,"highlight_end":23}],"label":"`ClusterStatistics` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3126,"line_start":131,"line_end":131,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":3144,"byte_end":3161,"line_start":132,"line_end":132,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl ClusterStatistics {","highlight_start":6,"highlight_end":23}],"label":"`ClusterStatistics` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:131:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ClusterStatistics {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusterStatistics` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusterStatistics` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5734,"line_start":246,"line_end":246,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":5752,"byte_end":5771,"line_start":247,"line_end":247,"column_start":6,"column_end":25,"is_primary":false,"text":[{"text":"impl VolatilityArchetype {","highlight_start":6,"highlight_end":25}],"label":"`VolatilityArchetype` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5734,"line_start":246,"line_end":246,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":5752,"byte_end":5771,"line_start":247,"line_end":247,"column_start":6,"column_end":25,"is_primary":false,"text":[{"text":"impl VolatilityArchetype {","highlight_start":6,"highlight_end":25}],"label":"`VolatilityArchetype` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:246:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m246\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m247\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl VolatilityArchetype {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityArchetype` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityArchetype` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6711,"line_start":278,"line_end":278,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":6729,"byte_end":6751,"line_start":279,"line_end":279,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl VolatilityRegimeResult {","highlight_start":6,"highlight_end":28}],"label":"`VolatilityRegimeResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6711,"line_start":278,"line_end":278,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":6729,"byte_end":6751,"line_start":279,"line_end":279,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl VolatilityRegimeResult {","highlight_start":6,"highlight_end":28}],"label":"`VolatilityRegimeResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:278:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl VolatilityRegimeResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityRegimeResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityRegimeResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8205,"line_start":318,"line_end":318,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":8223,"byte_end":8244,"line_start":319,"line_end":319,"column_start":6,"column_end":27,"is_primary":false,"text":[{"text":"impl DailyVolatilityVector {","highlight_start":6,"highlight_end":27}],"label":"`DailyVolatilityVector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8205,"line_start":318,"line_end":318,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":8223,"byte_end":8244,"line_start":319,"line_end":319,"column_start":6,"column_end":27,"is_primary":false,"text":[{"text":"impl DailyVolatilityVector {","highlight_start":6,"highlight_end":27}],"label":"`DailyVolatilityVector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:318:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m318\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DailyVolatilityVector {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9702,"line_start":364,"line_end":364,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":9720,"byte_end":9742,"line_start":365,"line_end":365,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl DailyVolatilityVectors {","highlight_start":6,"highlight_end":28}],"label":"`DailyVolatilityVectors` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9702,"line_start":364,"line_end":364,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":9720,"byte_end":9742,"line_start":365,"line_end":365,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl DailyVolatilityVectors {","highlight_start":6,"highlight_end":28}],"label":"`DailyVolatilityVectors` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:364:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m365\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DailyVolatilityVectors {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVectors` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVectors` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":19013,"byte_end":19013,"line_start":621,"line_end":621,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19013,"byte_end":19025,"line_start":621,"line_end":621,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19031,"byte_end":19042,"line_start":622,"line_end":622,"column_start":6,"column_end":17,"is_primary":false,"text":[{"text":"impl FxPriceData {","highlight_start":6,"highlight_end":17}],"label":"`FxPriceData` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19013,"byte_end":19013,"line_start":621,"line_end":621,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19013,"byte_end":19025,"line_start":621,"line_end":621,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19031,"byte_end":19042,"line_start":622,"line_end":622,"column_start":6,"column_end":17,"is_primary":false,"text":[{"text":"impl FxPriceData {","highlight_start":6,"highlight_end":17}],"label":"`FxPriceData` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19013,"byte_end":19025,"line_start":621,"line_end":621,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19013,"byte_end":19025,"line_start":621,"line_end":621,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:621:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m621\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m622\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl FxPriceData {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`FxPriceData` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`FxPriceData` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":19214,"byte_end":19214,"line_start":633,"line_end":633,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19214,"byte_end":19226,"line_start":633,"line_end":633,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19232,"byte_end":19248,"line_start":634,"line_end":634,"column_start":6,"column_end":22,"is_primary":false,"text":[{"text":"impl ClusteringResult {","highlight_start":6,"highlight_end":22}],"label":"`ClusteringResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19214,"byte_end":19214,"line_start":633,"line_end":633,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19214,"byte_end":19226,"line_start":633,"line_end":633,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19232,"byte_end":19248,"line_start":634,"line_end":634,"column_start":6,"column_end":22,"is_primary":false,"text":[{"text":"impl ClusteringResult {","highlight_start":6,"highlight_end":22}],"label":"`ClusteringResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19214,"byte_end":19226,"line_start":633,"line_end":633,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19214,"byte_end":19226,"line_start":633,"line_end":633,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:633:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m633\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m634\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ClusteringResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusteringResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusteringResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":19480,"byte_end":19480,"line_start":646,"line_end":646,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19480,"byte_end":19492,"line_start":646,"line_end":646,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19498,"byte_end":19515,"line_start":647,"line_end":647,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl VolatilityProfile {","highlight_start":6,"highlight_end":23}],"label":"`VolatilityProfile` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19480,"byte_end":19480,"line_start":646,"line_end":646,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19480,"byte_end":19492,"line_start":646,"line_end":646,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19498,"byte_end":19515,"line_start":647,"line_end":647,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl VolatilityProfile {","highlight_start":6,"highlight_end":23}],"label":"`VolatilityProfile` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19480,"byte_end":19492,"line_start":646,"line_end":646,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19480,"byte_end":19492,"line_start":646,"line_end":646,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:646:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m646\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m647\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl VolatilityProfile {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityProfile` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityProfile` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"14 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 14 warnings emitted\u001b[0m\n\n"}
