{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"default\", \"intel-mkl\", \"intel-mkl-static\", \"intel-mkl-system\", \"netlib\", \"netlib-static\", \"netlib-system\", \"openblas\", \"openblas-static\", \"openblas-system\"]", "target": 13331890148219350196, "profile": 15657897354478470176, "path": 6134431032675170810, "deps": [[514429886033515986, "kate<PERSON>t", false, 7398917580488880808], [3008854931152362171, "n<PERSON><PERSON>", false, 10211955169359649260], [3012893206755459258, "lax", false, 3717690255861923534], [5157631553186200874, "num_traits", false, 522175165424791702], [5913101255804133335, "cauchy", false, 989109775792363607], [8008191657135824715, "thiserror", false, 7965786736276243546], [12319020793864570031, "num_complex", false, 2890797963805629637], [13208667028893622512, "rand", false, 15105281158492510989]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ndarray-linalg-f85d611d0e477738\\dep-lib-ndarray_linalg", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}