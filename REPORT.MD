# Project Status Report: Dynamic FX Clustering Application

**Date:** 2025-07-06

## Executive Summary

This report provides a comprehensive overview of the current status of the Dynamic FX Clustering Application. The project aims to provide real-time correlation clustering and volatility regime detection for foreign exchange (FX) markets. Significant progress has been made on the frontend user interface, the Python backend, and the core Rust-based computational engine. Key functionalities such as correlation clustering and event detection are operational. However, critical issues with MT5 data connectivity and the incomplete implementation of volatility regime clustering in Rust are currently impacting the application's full functionality.

## Architecture Overview

The application employs a hybrid architecture designed for both flexibility and performance:

*   **Frontend (Python/Dash):** Built with Plotly Dash and Dash Bootstrap Components, providing an interactive web-based user interface for visualization and control.
*   **Python Backend:** Manages application state, handles data fetching from MetaTrader 5 (MT5), preprocesses data, and orchestrates calls to the Rust core.
*   **Rust Core:** A high-performance library responsible for computationally intensive tasks such as log returns calculation, correlation matrix computation, and clustering algorithms.
*   **Data Source:** MetaTrader 5, accessed via a dedicated Python connector.

The data flow generally proceeds from MT5 -> Python Data Manager -> Rust Core -> Python State Manager -> Dash Frontend.

## Completed Components

### Rust Core Engine Implementation Status

The foundational components of the Rust core have been successfully implemented and integrated:
*   **Data Structures:**
    *   [`FxPriceData`](cluster_core/src/lib.rs:44) for raw price inputs.
    *   [`LogReturns`](cluster_core/src/lib.rs:55) for processed log returns.
    *   [`CorrelationMatrix`](cluster_core/src/lib.rs:79) for correlation results.
    *   [`ClusteringResult`](cluster_core/src/lib.rs:103) encapsulating hierarchical clustering outcomes (cluster assignments, linkage matrix, silhouette score, cophenetic correlation).
    *   [`ClusterStatistics`](cluster_core/src/lib.rs:116) for aggregated cluster metrics.
    *   Base structures for volatility analysis (`DailyVolatilityVector`, `DailyVolatilityVectors`, `VolatilityArchetype`, `VolatilityRegimeResult`) are defined, though their clustering logic is pending.
*   **Core Functions:**
    *   [`calculate_log_returns`](cluster_core/src/lib.rs:573): Computes logarithmic returns from price data.
    *   [`compute_correlation_matrix`](cluster_core/src/lib.rs:466): Generates the Pearson correlation matrix.
    *   [`perform_hierarchical_clustering`](cluster_core/src/lib.rs:666): Executes hierarchical clustering on the correlation matrix.
    *   [`calculate_cluster_statistics`](cluster_core/src/lib.rs:753): Derives statistical insights for identified clusters.

### Python Backend Integration Status

The Python backend (`clustering/`) serves as the bridge between the frontend and the Rust core, managing data and application state:
*   **`ClusteringDataManager` (`clustering/data_manager.py`):**
    *   Handles connection to MT5 via `mt5_connector`.
    *   Fetches minute-level price data.
    *   Aligns price data across multiple symbols to ensure consistent timestamps (`_align_price_data`).
    *   Prepares and passes data to the Rust core, calling `calculate_log_returns` and `compute_correlation_matrix`.
*   **`ClusteringState` (`clustering/state_manager.py`):**
    *   Manages the overall application state, including historical data storage (`self.history`) and event logging (`self.event_log`).
    *   Orchestrates the data processing pipeline by calling `ClusteringDataManager` and then the Rust clustering functions.
    *   Implements `_detect_events` using `adjusted_rand_score` to identify significant cluster changes.
    *   Provides methods to retrieve cluster statistics (`get_cluster_statistics`) and generate Sankey diagram data (`get_sankey_data`) for visualization.
    *   Includes placeholder/mock implementations for volatility regime related classes and functions, indicating future Rust integration.

### Frontend UI Components Status

The Dash-based frontend (`run_clustering_app.py`) provides a rich, interactive user experience:
*   **Main Layout:** Features a tabbed interface for "Correlation Clustering" and "Volatility Regimes."
*   **Correlation Clustering Tab:**
    *   **Time Scrubber:** Allows navigation through historical clustering states with play/live controls.
    *   **Currency Correlation Dendrogram:** Visualizes hierarchical clustering results.
    *   **Cluster Evolution Timeline (Sankey Diagram):** Shows how currency pairs move between clusters over time.
    *   **Cluster Statistics Panel:** Displays detailed metrics for each cluster (members, average intra-correlation, volatility, cohesion).
    *   **Cluster Events Log:** Records significant changes in cluster assignments.
    *   **Event Details Modal:** Provides "Before and After" comparison for cluster events.
*   **Volatility Regimes Tab:**
    *   **Regime Analysis Controls:** Sliders for `n_clusters`, `timezone_offset`, and `analysis-period`.
    *   **Volatility Regime Calendar:** Visualizes daily volatility regimes (currently based on mock data).
    *   **Regime Legend and Summary Statistics:** Displays information about identified regimes.
    *   **Daily Drill-Down Modal:** For detailed volatility analysis on specific dates.
*   **Styling:** Utilizes `dash-bootstrap-components` and custom CSS (`assets/styles.css`) for a professional appearance.

### Data Flow and Architecture Status

The core data flow for correlation clustering is established and functional:
1.  **Data Fetching:** `ClusteringDataManager` requests data from MT5 via `MT5Connector`.
2.  **Python-Rust Interface:** `ClusteringDataManager` converts Pandas DataFrames into `FxPriceData` (Rust PyO3 binding) and passes it to Rust functions.
3.  **Rust Processing:** `cluster_core` performs `calculate_log_returns`, `compute_correlation_matrix`, and `perform_hierarchical_clustering`.
4.  **Result Handling:** Rust results are passed back to `ClusteringState` in Python.
5.  **State Management & Event Detection:** `ClusteringState` stores historical results, calculates cluster statistics, and detects events.
6.  **Visualization:** Data from `ClusteringState` is used by Dash callbacks to render graphs and panels.

## Current Issues & Limitations

### MT5 Connectivity Issues

The most critical current issue is the instability and errors in fetching data from MT5. The terminal logs frequently show:
*   `Error in _fetch_pair_data for EUR/USD: 'NoneType' object has no attribute 'total_seconds'`
*   `WARNING - No data retrieved for [currency pair]`
*   `ERROR - No data received from MT5`
*   `ERROR - Failed to fetch market data`

This indicates a problem within the `_fetch_pair_data` function in `matrix_QP/mt5_connector.py`, specifically at line 211: `offset_hours = start_time.utcoffset().total_seconds() / 3600`. This error occurs when `start_time` is a `datetime` object but is *naive* (i.e., lacks timezone information), causing `utcoffset()` to return `None`. The `mt5_connector` module relies on a `config` module (which was not available for review) for `MARKET_TIMEZONE` and `get_market_start_time`, which are crucial for correctly handling timezones. The `timedelta` logic in this function also needs to ensure timezone awareness is maintained consistently.

### Rust-Python Integration Status

While the initial integration for correlation clustering is robust, the integration for volatility regime detection is incomplete:
*   The Rust core has the necessary data structures (`DailyVolatilityVector`, `DailyVolatilityVectors`, `VolatilityArchetype`, `VolatilityRegimeResult`) defined.
*   However, the core functions for volatility calculation and clustering (`cluster_volatility_profiles` and `calculate_daily_volatility_vectors`) are currently commented out in `cluster_core/src/lib.rs` (line 454) and are mocked in Python's `clustering/state_manager.py` (lines 66-182). This means the volatility regime tab in the UI is displaying simulated data, not real-time computed results from Rust.

### Any Incomplete Features

*   **Volatility Regime Clustering (Rust):** The primary missing feature is the full implementation of the volatility regime clustering algorithms in Rust. This includes calculating daily volatility vectors from raw price data and then applying clustering (e.g., K-Means) to these vectors to identify different market regimes.
*   **Comprehensive Data Validation:** While some data quality checks are present in `mt5_connector.py`, further validation and error handling are needed across the entire data pipeline, especially for edge cases like missing data points or periods of low liquidity.

## Remaining Tasks

### Missing Features from the Original Specification

*   **Full Rust Implementation of Volatility Regime Detection:**
    *   Implement `calculate_daily_volatility_vectors` in Rust to process price data into daily hourly volatility profiles.
    *   Implement `cluster_volatility_profiles` in Rust using a suitable clustering algorithm (e.g., K-Means) to identify distinct volatility archetypes.
    *   Integrate these new Rust functions into the `ClusteringState` in Python, replacing the current mock implementations.

### Integration Issues to Resolve

*   **MT5 Data Fetching Reliability:**
    *   Thoroughly debug and fix the timezone handling in `mt5_connector.py`, ensuring that all `datetime` objects passed to `utcoffset()` are timezone-aware. This may require reviewing the `config` module and how `start_time` and `end_time` are generated and passed.
    *   Implement more robust retry mechanisms and error logging for MT5 connection and data fetching failures.
*   **Seamless Python-Rust Data Exchange:**
    *   While working, the current `FxPriceData` structure in Rust takes a flat `Vec<f64>` for prices. Consider if a more structured input (e.g., a 2D array or map of symbol to prices) would simplify Rust-side processing and reduce potential for misalignment.

### Testing and Deployment Needs

*   **Unit and Integration Testing:** Develop comprehensive test suites for:
    *   Rust core functions (numerical accuracy, edge cases).
    *   Python data management and state logic.
    *   MT5 connector functionality (connection, data fetching, error scenarios).
    *   Frontend component rendering and callback interactions.
*   **Deployment Strategy:**
    *   Define a clear process for building and packaging the Rust library (`cluster_core`) for deployment alongside the Python application (e.g., using `maturin` or `setuptools-rust`).
    *   Consider containerization (e.g., Docker) for simplified dependency management and deployment across different environments.

## Technical Implementation Details

### File Structure Overview

The project is organized into logical directories:

```
.
├── Dynamic Clustering for Real-Time FX Regime Detection.pdf
├── run_clustering_app.py           # Main Dash application entry point
├── spec.md                         # Project specification (external)
├── test_app.py                     # Frontend tests
├── test_volatility_calculation.py  # Volatility calculation tests
├── todo.md                         # Project TODOs
├── assets/
│   └── styles.css                  # Custom CSS for the Dash app
├── cluster_core/                   # Rust core library for computations
│   ├── .gitignore
│   ├── Cargo.lock
│   ├── Cargo.toml                  # Rust project manifest (dependencies: pyo3, ndarray, linfa, chrono, serde, log, etc.)
│   ├── pyproject.toml              # Python project configuration for Rust (e.g., maturin)
│   ├── README.md
│   ├── clustering/                 # (Potentially Rust sub-modules for clustering)
│   └── src/
│       └── lib.rs                  # Rust implementation of data structures and algorithms
├── clustering/                     # Python backend modules
│   ├── data_manager.py             # Handles MT5 data, Rust integration
│   └── state_manager.py            # Manages application state, historical data, event detection
└── matrix_QP/                      # External module (located outside current workspace)
    └── mt5_connector.py            # Connects to MetaTrader 5, fetches market data
    └── config.py                   # (Inferred) Configuration for MT5Connector
```

### Key Implemented Functions and Classes

*   **Python (`run_clustering_app.py`, `clustering/`):**
    *   `dash.Dash` app initialization.
    *   `ClusteringState` class (`clustering/state_manager.py`): Central state management, `update_state`, `_detect_events`, `get_cluster_statistics`, `get_sankey_data`, `update_volatility_regimes`.
    *   `ClusteringDataManager` class (`clustering/data_manager.py`): `fetch_minute_data`, `process_data_with_rust`, `_align_price_data`.
    *   `MT5Connector` class (`matrix_QP/mt5_connector.py`): `connect`, `disconnect`, `ensure_connection`, `fetch_daily_data`, `_fetch_pair_data`.
*   **Rust (`cluster_core/src/lib.rs`):**
    *   `FxPriceData`, `LogReturns`, `CorrelationMatrix`, `ClusteringResult`, `ClusterStatistics` structs (PyO3-bound).
    *   `DailyVolatilityVector`, `DailyVolatilityVectors`, `VolatilityArchetype`, `VolatilityRegimeResult` structs (PyO3-bound, for future implementation).
    *   `calculate_log_returns` function.
    *   `compute_correlation_matrix` function.
    *   `perform_hierarchical_clustering` function.
    *   `calculate_cluster_statistics` function.
    *   `CalculationError` enum for structured error handling.

### Architecture Decisions Made

*   **Language Choice:** Python for the UI and high-level logic due to its extensive libraries (Dash, Pandas, scikit-learn) and rapid development capabilities. Rust for the core computational engine to leverage its performance, memory safety, and strong type system for numerical stability.
*   **Interoperability:** `PyO3` is used to create seamless bindings between Python and Rust, allowing Python to directly call Rust functions and use Rust-defined data structures.
*   **Modularity:** The application is divided into distinct components (frontend, state management, data management, Rust core) to promote maintainability, testability, and scalability.
*   **Event-Driven UI:** Dash's callback mechanism facilitates an interactive and responsive user interface, updating visualizations dynamically based on user input or data changes.
*   **Logging:** `logging` module in Python and `log` crate in Rust are used for consistent application monitoring and debugging.

## Next Steps & Recommendations

### Priority Order for Remaining Tasks

1.  **High Priority: Resolve MT5 Data Fetching Errors.** This is critical as it's a blocking issue for real-time data analysis. Focus on debugging timezone handling in `mt5_connector.py` and ensuring `datetime` objects are timezone-aware before `utcoffset()` is called.
2.  **High Priority: Implement Volatility Regime Clustering in Rust.** This is a core missing feature that will complete the application's specified functionality. This includes both the daily volatility vector calculation and the clustering logic.
3.  **Medium Priority: Enhance Data Robustness and Error Handling.** Improve data validation and error propagation across the Python-Rust boundary. Address potential edge cases and ensure graceful degradation.
4.  **Medium Priority: Comprehensive Testing.** Develop unit and integration tests for all components to ensure reliability and prevent regressions.
5.  **Low Priority: Performance Optimization.** Once core functionality is stable, profile the application to identify and optimize performance bottlenecks, especially for large datasets or frequent updates.
6.  **Low Priority: Deployment Preparation.** Investigate and implement a robust deployment strategy, including packaging the Rust library and containerization.

### Deployment Considerations

*   **Rust Library Packaging:** Use `maturin` to build and publish the `cluster_core` Rust library as a Python wheel, simplifying its installation via `pip`.
*   **Containerization:** Dockerize the entire application (Python frontend + backend, Rust library, MT5 connector) to ensure consistent environments and easy deployment to various platforms. This will also help manage MT5 terminal dependencies.

### Performance Optimization Opportunities

*   **Asynchronous MT5 Data Fetching:** Explore non-blocking data fetching from MT5 to prevent UI freezes during long data retrieval operations.
*   **Rust Algorithm Optimization:** Continuously review and optimize Rust clustering algorithms for performance, especially as data volume increases. Leverage Rust's concurrency features if applicable.
*   **Dash Callback Optimization:** Optimize Dash callbacks to minimize unnecessary re-renders and ensure efficient data transfer between the backend and frontend. Consider using `dash.no_update` and optimizing graph updates.