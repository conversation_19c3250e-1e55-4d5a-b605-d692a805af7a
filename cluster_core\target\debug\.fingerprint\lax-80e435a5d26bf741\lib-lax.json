{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"default\", \"intel-mkl\", \"intel-mkl-src\", \"intel-mkl-static\", \"intel-mkl-system\", \"netlib\", \"netlib-src\", \"netlib-static\", \"netlib-system\", \"openblas\", \"openblas-src\", \"openblas-static\", \"openblas-system\"]", "target": 3161899464293606219, "profile": 2241668132362809309, "path": 7031619212554109141, "deps": [[265215447270525936, "lapack_sys", false, 17569232739308304045], [514429886033515986, "kate<PERSON>t", false, 7398917580488880808], [5157631553186200874, "num_traits", false, 8171727555973592493], [5913101255804133335, "cauchy", false, 12275881395028123239], [8008191657135824715, "thiserror", false, 8015957111518636185]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lax-80e435a5d26bf741\\dep-lib-lax", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}