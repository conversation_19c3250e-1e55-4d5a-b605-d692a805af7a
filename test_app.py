import dash
from dash import dcc, html, callback_context, Input, Output, State
import dash_bootstrap_components as dbc
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.figure_factory as ff
import plotly.express as px
import numpy as np
import json
from typing import Dict, List, Any

# Initialize the app with a professional Bootstrap theme
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.FLATLY],
    title="FX Correlation Clustering"
)

# Mock data for testing
CURRENCY_PAIRS = [
    "EUR/USD", "GBP/USD", "USD/JPY", "USD/CHF",
    "USD/CAD", "AUD/USD", "NZD/USD", "EUR/GBP",
    "EUR/JPY", "GBP/JPY"
]

# Mock clustering state
class MockClusteringState:
    def __init__(self):
        self.currency_pairs = CURRENCY_PAIRS
        self.event_log = []
        self.history = {}
        self._generate_mock_data()
    
    def _generate_mock_data(self):
        """Generate mock events for testing"""
        current_time = datetime.now()
        
        # Generate some mock events
        events = [
            {
                'timestamp': current_time - timedelta(hours=2),
                'type': 'cluster_change',
                'ari_score': 0.65,
                'affected_pairs': ['EUR/USD', 'GBP/USD', 'EUR/GBP'],
                'prev_clusters': [0, 0, 1, 1, 2, 2, 3, 0, 0, 1],
                'curr_clusters': [0, 1, 1, 1, 2, 2, 3, 0, 1, 1],
                'description': 'Major cluster restructuring affecting EUR and GBP pairs'
            },
            {
                'timestamp': current_time - timedelta(hours=1),
                'type': 'cluster_change',
                'ari_score': 0.72,
                'affected_pairs': ['USD/JPY', 'USD/CHF'],
                'prev_clusters': [0, 1, 1, 1, 2, 2, 3, 0, 1, 1],
                'curr_clusters': [0, 1, 2, 2, 2, 2, 3, 0, 1, 1],
                'description': 'USD pairs showing increased correlation'
            },
            {
                'timestamp': current_time - timedelta(minutes=30),
                'type': 'cluster_change',
                'ari_score': 0.58,
                'affected_pairs': ['AUD/USD', 'NZD/USD'],
                'prev_clusters': [0, 1, 2, 2, 2, 2, 3, 0, 1, 1],
                'curr_clusters': [0, 1, 2, 2, 2, 3, 3, 0, 1, 1],
                'description': 'Commodity currency pairs diverging from USD cluster'
            }
        ]
        
        self.event_log = events
        
        # Generate mock history
        for event in events:
            self.history[event['timestamp']] = {
                'cluster_assignments': np.array(event['curr_clusters']),
                'cluster_statistics': []
            }
    
    def update_state(self):
        return True

# Initialize mock state
state_manager = MockClusteringState()

# Mock volatility regime data for testing
def generate_mock_volatility_data():
    """Generate mock volatility data for testing the daily drill-down modal"""
    import random
    
    # Generate mock 24-hour volatility vector for a selected day
    volatility_vector = []
    for hour in range(24):
        # Create realistic volatility patterns based on trading sessions
        if 0 <= hour <= 6:  # Asian session
            base_vol = 0.03 + random.uniform(-0.01, 0.01)
        elif 8 <= hour <= 16:  # European/US overlap
            base_vol = 0.05 + random.uniform(-0.02, 0.02)
        elif 13 <= hour <= 21:  # US session
            base_vol = 0.06 + random.uniform(-0.015, 0.015)
        else:  # Transition periods
            base_vol = 0.04 + random.uniform(-0.015, 0.015)
        
        volatility_vector.append(max(0.01, base_vol))
    
    return volatility_vector

def generate_mock_regime_timeline():
    """Generate mock regime timeline for 24 hours"""
    regimes = ["Asian Session", "European Session", "US Session", "High Volatility"]
    regime_colors = {"Asian Session": "#2E8B57", "European Session": "#4169E1",
                    "US Session": "#DC143C", "High Volatility": "#FF8C00"}
    
    timeline = []
    for hour in range(24):
        if 0 <= hour <= 6:
            regime = "Asian Session"
        elif 8 <= hour <= 16:
            regime = "European Session"
        elif 13 <= hour <= 21:
            regime = "US Session"
        else:
            regime = "High Volatility"
        
        timeline.append({
            'hour': hour,
            'regime': regime,
            'color': regime_colors[regime]
        })
    
    return timeline

# Add mock method to state manager for testing
def mock_get_intraday_regime_match(selected_date):
    """Mock implementation of get_intraday_regime_match for testing"""
    import random
    
    regimes = ["Asian Session", "European Session", "US Session", "High Volatility"]
    hourly_matches = []
    
    for hour in range(24):
        regime_id = random.randint(0, 3)
        hourly_matches.append({
            'hour': hour,
            'regime_id': regime_id,
            'regime_name': regimes[regime_id],
            'confidence': 0.7 + random.uniform(-0.2, 0.2)
        })
    
    return {
        'selected_date': selected_date,
        'hourly_matches': hourly_matches,
        'available_regimes': [
            {'regime_id': i, 'regime_name': name, 'mean_volatility': 0.04 + i*0.01}
            for i, name in enumerate(regimes)
        ]
    }

# Add the mock method to state manager
state_manager.get_intraday_regime_match = mock_get_intraday_regime_match

# Create placeholder figures
def create_placeholder_figure(title):
    return go.Figure().update_layout(
        title=title,
        xaxis_visible=False,
        yaxis_visible=False,
        annotations=[{
            "text": "Mock data - functionality testing",
            "xref": "paper",
            "yref": "paper",
            "showarrow": False,
            "font": {"size": 20}
        }]
    )

# Define color scheme for consistency
COLOR_SCHEME = [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
]

# Layout components
time_controls = dbc.Card([
    dbc.CardBody([
        dcc.Slider(
            id='time-slider',
            min=0,
            max=100,
            step=1,
            value=100,
            marks=None,
            tooltip={"placement": "bottom", "always_visible": True}
        ),
        html.Div(id='time-display', className="text-center mt-2")
    ])
], className="mb-4")

# Define the 4-panel layout
app.layout = dbc.Container([
    # Hidden components for state management
    dcc.Store(id='cluster-data-store'),
    dcc.Interval(id='update-interval', interval=30*1000),  # 30 seconds
    
    # Title row
    dbc.Row([
        dbc.Col([
            html.H1("FX Correlation Clustering Analysis - Event Log Test", className="text-primary text-center my-4")
        ])
    ]),
    
    # Time control row
    dbc.Row([
        dbc.Col([time_controls])
    ]),
    
    # Top panels row
    dbc.Row([
        # Dendrogram panel
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H4("Currency Correlation Dendrogram", className="m-0")
                ]),
                dbc.CardBody([
                    dcc.Graph(
                        id='dendrogram-graph',
                        figure=create_placeholder_figure("Currency Correlation Dendrogram"),
                        config={'displayModeBar': True}
                    )
                ])
            ], className="h-100")
        ], width=6),
        
        # Sankey diagram panel
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H4("Cluster Evolution Timeline", className="m-0")
                ]),
                dbc.CardBody([
                    dcc.Graph(
                        id='sankey-graph',
                        figure=create_placeholder_figure("Cluster Evolution Timeline"),
                        config={'displayModeBar': True}
                    )
                ])
            ], className="h-100")
        ], width=6)
    ], className="mb-4"),
    
    # Bottom panels row
    dbc.Row([
        # Statistics panel
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H4("Cluster Statistics", className="m-0")
                ]),
                dbc.CardBody([
                    html.Div(id='stats-content', className="stats-panel")
                ])
            ], className="h-100")
        ], width=6),
        
        # Event log panel
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H4("Cluster Events", className="m-0")
                ]),
                dbc.CardBody([
                    dcc.Loading(
                        id="events-loading",
                        type="default",
                        children=html.Div(id='events-content', className="events-panel")
                    )
                ])
            ], className="h-100")
        ], width=6)
    ]),
    
    # "Before and After" Modal for event details
    dbc.Modal([
        dbc.ModalHeader([
            dbc.ModalTitle("Event Details - Before and After Comparison", id="modal-title")
        ]),
        dbc.ModalBody([
            dcc.Loading(
                id="modal-loading",
                type="default",
                children=html.Div(id="modal-content")
            )
        ]),
        dbc.ModalFooter([
            dbc.Button(
                "Close",
                id="modal-close-button",
                className="ms-auto",
                color="secondary",
                n_clicks=0
            )
        ])
    ], id="event-modal", size="xl", scrollable=True, is_open=False)
    
], fluid=True, className="p-4")

# Event Log callback
@app.callback(
    dash.Output('events-content', 'children'),
    [dash.Input('update-interval', 'n_intervals')]
)
def update_event_log(n_intervals):
    """Update event log with interactive table of cluster events"""
    try:
        # Get event log from state manager
        events = state_manager.event_log
        
        if not events:
            return html.Div([
                html.P("No cluster events detected yet.", className="text-muted text-center mb-0")
            ])
        
        # Sort events by timestamp (newest first)
        sorted_events = sorted(events, key=lambda x: x['timestamp'], reverse=True)
        
        # Create table headers
        table_header = html.Thead([
            html.Tr([
                html.Th("Timestamp", className="text-center"),
                html.Th("Type", className="text-center"),
                html.Th("Description", className="text-center"),
                html.Th("ARI Score", className="text-center"),
                html.Th("Actions", className="text-center")
            ])
        ])
        
        # Create table rows
        table_rows = []
        for i, event in enumerate(sorted_events):
            # Determine event type display
            event_type = event.get('type', 'cluster_change')
            event_badge_color = "warning" if event_type == "cluster_change" else "info"
            
            # Format timestamp
            timestamp_str = event['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            
            # Create table row
            row = html.Tr([
                html.Td(timestamp_str, className="text-center"),
                html.Td([
                    dbc.Badge(
                        event_type.replace('_', ' ').title(),
                        color=event_badge_color,
                        className="me-1"
                    )
                ], className="text-center"),
                html.Td(event.get('description', 'N/A'), className="text-center"),
                html.Td(f"{event.get('ari_score', 0):.3f}", className="text-center"),
                html.Td([
                    dbc.Button(
                        "Details",
                        id={"type": "event-detail-btn", "index": i},
                        color="primary",
                        size="sm",
                        outline=True,
                        className="me-1"
                    )
                ], className="text-center")
            ], className="align-middle")
            
            table_rows.append(row)
        
        # Create table body
        table_body = html.Tbody(table_rows)
        
        # Create complete table
        table = dbc.Table([table_header, table_body], 
                         striped=True, 
                         bordered=True, 
                         hover=True, 
                         responsive=True,
                         className="mb-0")
        
        return html.Div([
            html.Div([
                html.H6(f"Recent Events ({len(sorted_events)} total)", className="mb-3")
            ]),
            table
        ])
        
    except Exception as e:
        print(f"Error updating event log: {str(e)}")
        return html.Div([
            html.P("Error loading event log.", className="text-danger text-center mb-0")
        ])

# Modal callback for event details
@app.callback(
    [
        dash.Output('event-modal', 'is_open'),
        dash.Output('modal-content', 'children'),
        dash.Output('modal-title', 'children')
    ],
    [
        dash.Input({'type': 'event-detail-btn', 'index': dash.dependencies.ALL}, 'n_clicks'),
        dash.Input('modal-close-button', 'n_clicks')
    ],
    [dash.State('event-modal', 'is_open')]
)
def toggle_event_modal(detail_clicks, close_clicks, is_open):
    """Handle event detail modal opening and closing"""
    ctx = callback_context
    
    if not ctx.triggered:
        return False, html.Div(), "Event Details"
    
    # Get the trigger source
    trigger_id = ctx.triggered[0]['prop_id']
    
    # Handle close button
    if 'modal-close-button' in trigger_id:
        return False, html.Div(), "Event Details"
    
    # Handle detail button clicks
    if 'event-detail-btn' in trigger_id and any(detail_clicks):
        try:
            # Find which button was clicked
            clicked_index = None
            for i, clicks in enumerate(detail_clicks):
                if clicks and clicks > 0:
                    clicked_index = i
                    break
            
            if clicked_index is None:
                return False, html.Div(), "Event Details"
            
            # Get the event data
            events = state_manager.event_log
            if not events or clicked_index >= len(events):
                return False, html.Div(), "Event Details"
            
            # Sort events by timestamp (newest first) to match the table
            sorted_events = sorted(events, key=lambda x: x['timestamp'], reverse=True)
            event = sorted_events[clicked_index]
            
            # Create modal content
            modal_content = create_event_comparison_content(event)
            
            # Create modal title
            timestamp_str = event['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            modal_title = f"Event Details - {timestamp_str}"
            
            return True, modal_content, modal_title
            
        except Exception as e:
            print(f"Error opening event modal: {str(e)}")
            error_content = html.Div([
                dbc.Alert(
                    "Error loading event details. Please try again.",
                    color="danger",
                    className="mb-0"
                )
            ])
            return True, error_content, "Error"
    
    return is_open, html.Div(), "Event Details"

def create_event_comparison_content(event):
    """Create the before/after comparison content for the modal"""
    try:
        # Create comparison content
        content = html.Div([
            # Event summary
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Event Summary", className="mb-0")
                ]),
                dbc.CardBody([
                    html.Div([
                        html.Strong("Type: "), event.get('type', 'cluster_change').replace('_', ' ').title()
                    ], className="mb-2"),
                    html.Div([
                        html.Strong("Description: "), event.get('description', 'N/A')
                    ], className="mb-2"),
                    html.Div([
                        html.Strong("Adjusted Rand Index: "), f"{event.get('ari_score', 0):.3f}"
                    ], className="mb-2"),
                    html.Div([
                        html.Strong("Affected Pairs: "), 
                        ", ".join(event.get('affected_pairs', []))
                    ], className="mb-0")
                ])
            ], className="mb-4"),
            
            # Before and After comparison
            dbc.Row([
                # Before state
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H5("Before", className="mb-0 text-info")
                        ]),
                        dbc.CardBody([
                            create_cluster_state_summary(event.get('prev_clusters', []), "before")
                        ])
                    ])
                ], width=6),
                
                # After state  
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H5("After", className="mb-0 text-success")
                        ]),
                        dbc.CardBody([
                            create_cluster_state_summary(event.get('curr_clusters', []), "after")
                        ])
                    ])
                ], width=6)
            ], className="mb-4"),
            
            # Detailed changes
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Detailed Changes", className="mb-0")
                ]),
                dbc.CardBody([
                    create_detailed_changes_table(event)
                ])
            ])
        ])
        
        return content
        
    except Exception as e:
        print(f"Error creating event comparison content: {str(e)}")
        return html.Div([
            dbc.Alert(
                "Error generating comparison content.",
                color="danger",
                className="mb-0"
            )
        ])

def create_cluster_state_summary(clusters, prefix):
    """Create a summary of cluster state"""
    try:
        if not clusters:
            return html.P("No cluster data available.")
        
        n_clusters = len(set(clusters))
        
        # Group pairs by cluster
        cluster_groups = {}
        for i, cluster_id in enumerate(clusters):
            if cluster_id not in cluster_groups:
                cluster_groups[cluster_id] = []
            cluster_groups[cluster_id].append(CURRENCY_PAIRS[i])
        
        # Create cluster summary
        cluster_items = []
        for cluster_id, pairs in sorted(cluster_groups.items()):
            cluster_items.append(
                html.Div([
                    html.Strong(f"Cluster {cluster_id}: "),
                    ", ".join(pairs)
                ], className="mb-2")
            )
        
        return html.Div([
            html.Div([
                html.Strong("Total Clusters: "), str(n_clusters)
            ], className="mb-3"),
            html.Div([
                html.Strong("Cluster Composition:")
            ], className="mb-2"),
            html.Div(cluster_items)
        ])
        
    except Exception as e:
        print(f"Error creating cluster state summary: {str(e)}")
        return html.P("Error loading cluster state summary.")

def create_detailed_changes_table(event):
    """Create a detailed table of changes between states"""
    try:
        prev_clusters = event.get('prev_clusters', [])
        curr_clusters = event.get('curr_clusters', [])
        
        if not prev_clusters or not curr_clusters:
            return html.P("Detailed change data not available.")
        
        # Create table of changes
        table_header = html.Thead([
            html.Tr([
                html.Th("Currency Pair", className="text-center"),
                html.Th("Before Cluster", className="text-center"),
                html.Th("After Cluster", className="text-center"),
                html.Th("Change", className="text-center")
            ])
        ])
        
        table_rows = []
        for i, (prev_cluster, curr_cluster) in enumerate(zip(prev_clusters, curr_clusters)):
            pair = CURRENCY_PAIRS[i]
            
            # Determine if there was a change
            changed = prev_cluster != curr_cluster
            change_badge = dbc.Badge(
                "Changed" if changed else "No Change",
                color="warning" if changed else "success",
                className="me-1"
            )
            
            row = html.Tr([
                html.Td(pair, className="text-center"),
                html.Td(str(prev_cluster), className="text-center"),
                html.Td(str(curr_cluster), className="text-center"),
                html.Td(change_badge, className="text-center")
            ], className="align-middle")
            
            table_rows.append(row)
        
        table_body = html.Tbody(table_rows)
        
        return dbc.Table([table_header, table_body], 
                        striped=True, 
                        bordered=True, 
                        hover=True, 
                        responsive=True,
                        className="mb-0")
        
    except Exception as e:
        print(f"Error creating detailed changes table: {str(e)}")
        return html.P("Error loading detailed changes.")

# Run the server
if __name__ == '__main__':
    app.run_server(debug=True, port=8051)