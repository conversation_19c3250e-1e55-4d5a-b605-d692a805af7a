{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3987260608810180118, "build_script_build", false, 9817466848059683519]], "local": [{"RerunIfEnvChanged": {"var": "PYO3_CONFIG_FILE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_NO_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_ENVIRONMENT_SIGNATURE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "VIRTUAL_ENV", "val": null}}, {"RerunIfEnvChanged": {"var": "CONDA_PREFIX", "val": null}}, {"RerunIfEnvChanged": {"var": "PATH", "val": "C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Microsoft VS Code\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Process Lasso\\;C:\\Program Files\\Git\\cmd;C:\\Users\\<USER>\\.cargo\\bin;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\bin"}}], "rustflags": [], "config": 0, "compile_kind": 0}