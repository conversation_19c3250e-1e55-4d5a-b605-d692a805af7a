{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"benchmarks\", \"blas\", \"criterion\", \"default\", \"intel-mkl-static\", \"intel-mkl-system\", \"ndarray-linalg\", \"netlib-static\", \"netlib-system\", \"openblas-static\", \"openblas-system\", \"pprof\", \"serde\", \"serde_crate\"]", "target": 11587406923222373526, "profile": 2241668132362809309, "path": 5004194065723628634, "deps": [[2289341005599476083, "approx", false, 11827345385470238255], [3008854931152362171, "n<PERSON><PERSON>", false, 16633511415824908303], [3208259674829469253, "build_script_build", false, 4804814527549525913], [5157631553186200874, "num_traits", false, 8171727555973592493], [8008191657135824715, "thiserror", false, 8015957111518636185], [11422141427705954375, "sprs", false, 8977832066109898496], [13208667028893622512, "rand", false, 15746186270168620571]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\linfa-931fd98d3c64a933\\dep-lib-linfa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}