"""
MT5 Trader Module for Matrix QP
Handles sending portfolio trades directly to MetaTrader 5
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import time

from config import (
    MT5_TIMEOUT, MAX_RETRIES, RETRY_DELAY
)

# Configure logging
logger = logging.getLogger(__name__)


class MT5Trader:
    """Handles trade execution for portfolio optimization results"""
    
    def __init__(self, mt5_connector=None):
        """
        Initialize MT5 trader
        
        Args:
            mt5_connector: Optional MT5Connector instance for connection management
        """
        self.mt5_connector = mt5_connector
        self.connected = False
        
    def connect(self) -> bool:
        """
        Connect to MT5 if not already connected
        
        Returns:
            bool: True if connected successfully
        """
        if self.mt5_connector and self.mt5_connector.is_connected():
            self.connected = True
            return True
            
        # Try direct connection if no connector provided
        try:
            if mt5.initialize(path="E:\\icdemomt5\\terminal64.exe", portable=True):
                self.connected = True
                logger.info("MT5Trader connected successfully")
                return True
            else:
                logger.error(f"MT5Trader connection failed: {mt5.last_error()}")
                return False
        except Exception as e:
            logger.error(f"MT5Trader connection exception: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5"""
        if not self.mt5_connector:  # Only disconnect if we made the connection
            mt5.shutdown()
        self.connected = False
        
    def send_portfolio_to_mt5(self, 
                             portfolio_data: Dict, 
                             lot_size: float = 1.0,
                             close_existing: bool = True) -> Dict:
        """
        Send portfolio trades to MT5
        
        Args:
            portfolio_data: Portfolio data with pairs, weights, and strategy
            lot_size: Base lot size for position sizing
            close_existing: Whether to close existing positions first
            
        Returns:
            Dict with execution results
        """
        try:
            if not self.connected and not self.connect():
                return {
                    'success': False,
                    'error': 'Failed to connect to MT5',
                    'trades': []
                }
            
            pairs = portfolio_data.get('pairs', [])
            weights = portfolio_data.get('weights', [])
            strategy = portfolio_data.get('strategy', 'unknown')
            
            if len(pairs) != len(weights):
                return {
                    'success': False,
                    'error': 'Pairs and weights length mismatch',
                    'trades': []
                }
            
            logger.info(f"Sending {strategy} portfolio to MT5: {len(pairs)} pairs")
            
            results = {
                'success': True,
                'strategy': strategy,
                'trades': [],
                'errors': []
            }
            
            # Close existing positions if requested
            if close_existing:
                close_results = self._close_all_positions()
                if close_results['errors']:
                    results['errors'].extend(close_results['errors'])
            
            # Execute new trades
            for pair, weight in zip(pairs, weights):
                trade_result = self._execute_trade(pair, weight, lot_size, strategy)
                results['trades'].append(trade_result)

                if not trade_result['success']:
                    error_msg = f"Failed to trade {pair}: {trade_result['error']}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
                else:
                    logger.info(f"Successfully executed trade: {trade_result['comment']}")

            # Check overall success
            successful_trades = sum(1 for trade in results['trades'] if trade['success'])
            results['success'] = successful_trades > 0
            results['summary'] = f"Executed {successful_trades}/{len(pairs)} trades successfully"

            logger.info(f"Portfolio execution complete: {results['summary']}")
            if results['errors']:
                logger.error(f"Trade errors: {results['errors']}")
            return results
            
        except Exception as e:
            logger.error(f"Portfolio execution failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'trades': []
            }
    
    def _execute_trade(self, pair: str, weight: float, lot_size: float, strategy: str = 'unknown') -> Dict:
        """
        Execute a single trade
        
        Args:
            pair: Currency pair symbol
            weight: Portfolio weight (positive for buy, negative for sell)
            lot_size: Base lot size
            
        Returns:
            Dict with trade execution result
        """
        try:
            # Calculate position size
            position_size = abs(weight) * lot_size

            # Determine trade direction
            trade_type = mt5.ORDER_TYPE_BUY if weight > 0 else mt5.ORDER_TYPE_SELL
            action_str = "BUY" if weight > 0 else "SELL"

            logger.info(f"Attempting to execute {action_str} {position_size} lots of {pair} (weight: {weight})")

            # Get current price
            symbol_info = mt5.symbol_info(pair)
            if symbol_info is None:
                error_msg = f'Symbol {pair} not found'
                logger.error(error_msg)
                return {
                    'success': False,
                    'pair': pair,
                    'error': error_msg
                }
            
            if not symbol_info.visible:
                # Try to enable symbol
                if not mt5.symbol_select(pair, True):
                    return {
                        'success': False,
                        'pair': pair,
                        'error': f'Failed to enable symbol {pair}'
                    }
            
            # Get current tick
            tick = mt5.symbol_info_tick(pair)
            if tick is None:
                return {
                    'success': False,
                    'pair': pair,
                    'error': f'Failed to get tick data for {pair}'
                }
            
            price = tick.ask if trade_type == mt5.ORDER_TYPE_BUY else tick.bid
            
            # Calculate stop loss (50 pips)
            symbol_info = mt5.symbol_info(pair)
            point = symbol_info.point
            sl_distance = 50 * point * 10  # 50 pips converted to price distance

            if trade_type == mt5.ORDER_TYPE_BUY:
                sl_price = price - sl_distance
            else:  # SELL
                sl_price = price + sl_distance

            # Prepare trade request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": pair,
                "volume": round(position_size, 2),
                "type": trade_type,
                "price": price,
                "sl": round(sl_price, symbol_info.digits),  # 50 pip stop loss
                "tp": 0.0,  # No take profit as requested
                "deviation": 20,
                "magic": 123456,  # Magic number for identification
                "comment": f"{strategy[:8]}_QP",  # Limit comment length to avoid MT5 errors
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Send trade request
            logger.info(f"Sending trade request: {request}")
            result = mt5.order_send(request)

            # Check for MT5 errors
            last_error = mt5.last_error()
            logger.info(f"Trade result: {result}, MT5 last_error: {last_error}")

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f'Trade failed: {result.comment} (code: {result.retcode})'
                if last_error[0] != 0:
                    error_msg += f', MT5 error: {last_error}'
                logger.error(f"Trade execution failed for {pair}: {error_msg}")
                return {
                    'success': False,
                    'pair': pair,
                    'weight': weight,
                    'error': error_msg
                }
            
            return {
                'success': True,
                'pair': pair,
                'weight': weight,
                'action': action_str,
                'volume': position_size,
                'price': price,
                'ticket': result.order,
                'comment': f'Executed {action_str} {position_size} lots of {pair} at {price}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'pair': pair,
                'weight': weight,
                'error': str(e)
            }
    
    def _close_all_positions(self) -> Dict:
        """
        Close all open positions
        
        Returns:
            Dict with closing results
        """
        try:
            positions = mt5.positions_get()
            if positions is None:
                return {'closed': 0, 'errors': []}
            
            results = {'closed': 0, 'errors': []}
            
            for position in positions:
                try:
                    # Prepare close request
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "symbol": position.symbol,
                        "volume": position.volume,
                        "type": mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                        "position": position.ticket,
                        "deviation": 20,
                        "magic": 123456,
                        "comment": "QP Close",  # Shortened comment to avoid MT5 length limits
                        "type_time": mt5.ORDER_TIME_GTC,
                        "type_filling": mt5.ORDER_FILLING_IOC,
                    }
                    
                    # Get current price for closing
                    tick = mt5.symbol_info_tick(position.symbol)
                    if tick:
                        close_request["price"] = tick.bid if position.type == mt5.ORDER_TYPE_BUY else tick.ask
                    
                    result = mt5.order_send(close_request)

                    # Check for MT5 errors
                    last_error = mt5.last_error()
                    logger.info(f"Close result: {result}, MT5 last_error: {last_error}")

                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        results['closed'] += 1
                        logger.info(f"Closed position {position.ticket} for {position.symbol}")
                    else:
                        error_msg = f"Failed to close {position.symbol}: {result.comment}"
                        if last_error[0] != 0:
                            error_msg += f", MT5 error: {last_error}"
                        results['errors'].append(error_msg)
                        logger.warning(error_msg)
                        
                except Exception as e:
                    error_msg = f"Error closing position {position.symbol}: {str(e)}"
                    results['errors'].append(error_msg)
                    logger.error(error_msg)
            
            logger.info(f"Closed {results['closed']} positions with {len(results['errors'])} errors")
            return results
            
        except Exception as e:
            logger.error(f"Error in close_all_positions: {str(e)}")
            return {'closed': 0, 'errors': [str(e)]}
    
    def get_account_info(self) -> Optional[Dict]:
        """
        Get MT5 account information
        
        Returns:
            Dict with account info or None if failed
        """
        try:
            if not self.connected and not self.connect():
                return None
                
            account_info = mt5.account_info()
            if account_info is None:
                return None
                
            return {
                'login': account_info.login,
                'server': account_info.server,
                'currency': account_info.currency,
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'margin_level': account_info.margin_level
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {str(e)}")
            return None


if __name__ == "__main__":
    # Test the trader
    logging.basicConfig(level=logging.INFO)
    
    trader = MT5Trader()
    
    # Test connection
    if trader.connect():
        print("✓ Connected to MT5")
        
        # Test account info
        account = trader.get_account_info()
        if account:
            print(f"✓ Account: {account['login']} on {account['server']}")
            print(f"  Balance: {account['balance']} {account['currency']}")
        
        trader.disconnect()
    else:
        print("✗ Failed to connect to MT5")
