{"rustc": 1842507548689473721, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 12553840366118088266, "deps": [[555019317135488525, "regex_automata", false, 15323972047766748884], [2779309023524819297, "aho_corasick", false, 15587445673311145726], [9408802513701742484, "regex_syntax", false, 13692133560594494553], [15932120279885307830, "memchr", false, 356450998705185125]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-eb364a3b59769e5d\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}