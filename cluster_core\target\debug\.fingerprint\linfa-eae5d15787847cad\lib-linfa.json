{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"benchmarks\", \"blas\", \"criterion\", \"default\", \"intel-mkl-static\", \"intel-mkl-system\", \"ndarray-linalg\", \"netlib-static\", \"netlib-system\", \"openblas-static\", \"openblas-system\", \"pprof\", \"serde\", \"serde_crate\"]", "target": 11587406923222373526, "profile": 15657897354478470176, "path": 5004194065723628634, "deps": [[2289341005599476083, "approx", false, 12642980302962487367], [3008854931152362171, "n<PERSON><PERSON>", false, 10211955169359649260], [3208259674829469253, "build_script_build", false, 4804814527549525913], [5157631553186200874, "num_traits", false, 522175165424791702], [8008191657135824715, "thiserror", false, 7965786736276243546], [11422141427705954375, "sprs", false, 10717965965621303639], [13208667028893622512, "rand", false, 15105281158492510989]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\linfa-eae5d15787847cad\\dep-lib-linfa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}