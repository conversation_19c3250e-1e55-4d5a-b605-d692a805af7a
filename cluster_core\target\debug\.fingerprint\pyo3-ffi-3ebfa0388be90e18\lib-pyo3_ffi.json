{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 13199778606205993442, "profile": 15657897354478470176, "path": 6914790444347254751, "deps": [[4684437522915235464, "libc", false, 17776186151666319176], [9128542196482641081, "build_script_build", false, 6371193526265635658]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-ffi-3ebfa0388be90e18\\dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}