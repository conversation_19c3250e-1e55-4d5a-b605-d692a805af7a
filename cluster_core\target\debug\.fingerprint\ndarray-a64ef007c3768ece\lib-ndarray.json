{"rustc": 1842507548689473721, "features": "[\"approx\", \"blas\", \"cblas-sys\", \"default\", \"libc\", \"rayon\", \"rayon_\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 15657897354478470176, "path": 2525462169438355074, "deps": [[2289341005599476083, "approx", false, 12642980302962487367], [4684437522915235464, "libc", false, 17776186151666319176], [5157631553186200874, "num_traits", false, 522175165424791702], [6959212579930910503, "cblas_sys", false, 10980198319340900820], [10697383615564341592, "rayon_", false, 3329352285143233302], [12319020793864570031, "num_complex", false, 2890797963805629637], [15709748443193639506, "rawpointer", false, 13738660862367348416], [15826188163127377936, "matrixmultiply", false, 3376436245802699118], [16795989132585092538, "num_integer", false, 16313469475458269161]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ndarray-a64ef007c3768ece\\dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}