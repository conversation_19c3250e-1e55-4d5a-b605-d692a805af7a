import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from sklearn.metrics import adjusted_rand_score
from .data_manager import ClusteringDataManager

# Import Rust functions
from cluster_core import (
    calculate_log_returns,
    compute_correlation_matrix,
    perform_hierarchical_clustering,
    calculate_cluster_statistics,
    FxPriceData
)

# Mock volatility clustering classes until Rust implementation is ready
class DailyVolatilityVector:
    def __init__(self, symbol, date, timestamp):
        self.symbol = symbol
        self.date = date
        self.timestamp = timestamp
        self.hourly_volatility = []
        self.mean_volatility = 0.0
        self.std_volatility = 0.0
        self.peak_hour = 0
        self.quiet_hour = 0
        self.data_completeness = 0.0

class DailyVolatilityVectors:
    def __init__(self):
        self.vectors = []
        self.symbols = []
        self.date_range = []
        self.total_days = 0
        self.avg_data_completeness = 0.0
        self.timezone = "UTC"

class VolatilityArchetype:
    def __init__(self, regime_id):
        self.regime_id = regime_id
        self.hourly_pattern = []
        self.mean_volatility = 0.0
        self.std_volatility = 0.0
        self.peak_hour = 0
        self.quiet_hour = 0
        self.regime_name = f"Regime_{regime_id}"
        self.member_count = 0
        self.intra_regime_coherence = 0.0

class VolatilityRegimeResult:
    def __init__(self):
        self.cluster_assignments = []
        self.archetypes = []
        self.n_clusters = 0
        self.within_cluster_sum_of_squares = 0.0
        self.total_sum_of_squares = 0.0
        self.silhouette_score = 0.0
        self.calinski_harabasz_score = 0.0
        self.inertia = 0.0
        self.n_iterations = 0
        self.converged = False
        self.processing_time_ms = 0

# Mock volatility clustering functions until Rust implementation is ready
def calculate_daily_volatility_vectors(price_data, timezone_offset=0):
    """Mock implementation of daily volatility vectors calculation"""
    import random
    from datetime import datetime, timedelta
    
    # Create mock daily volatility vectors
    vectors = []
    symbols = price_data.symbols
    
    # Generate data for last 30 days
    base_date = datetime.now() - timedelta(days=30)
    
    for day in range(30):
        current_date = base_date + timedelta(days=day)
        date_str = current_date.strftime('%Y-%m-%d')
        
        for symbol in symbols:
            # Generate realistic hourly volatility pattern
            hourly_vol = []
            for hour in range(24):
                # Create different volatility patterns based on hour
                if 8 <= hour <= 16:  # European/US overlap
                    base_vol = 0.05 + random.uniform(-0.02, 0.02)
                elif 0 <= hour <= 6:  # Asian session
                    base_vol = 0.03 + random.uniform(-0.01, 0.01)
                else:  # Transition periods
                    base_vol = 0.04 + random.uniform(-0.015, 0.015)
                
                hourly_vol.append(max(0.01, base_vol))
            
            # Create DailyVolatilityVector
            vector = DailyVolatilityVector(symbol, date_str, int(current_date.timestamp()))
            vector.hourly_volatility = hourly_vol
            vector.mean_volatility = sum(hourly_vol) / len(hourly_vol)
            vector.std_volatility = (sum((v - vector.mean_volatility)**2 for v in hourly_vol) / len(hourly_vol))**0.5
            vector.peak_hour = hourly_vol.index(max(hourly_vol))
            vector.quiet_hour = hourly_vol.index(min(hourly_vol))
            vector.data_completeness = 0.95 + random.uniform(-0.05, 0.05)
            
            vectors.append(vector)
    
    # Create DailyVolatilityVectors container
    daily_vectors = DailyVolatilityVectors()
    daily_vectors.vectors = vectors
    daily_vectors.symbols = symbols
    daily_vectors.date_range = [
        (base_date).strftime('%Y-%m-%d'),
        (base_date + timedelta(days=29)).strftime('%Y-%m-%d')
    ]
    daily_vectors.total_days = 30
    daily_vectors.avg_data_completeness = 0.95
    daily_vectors.timezone = f"UTC{timezone_offset:+d}"
    
    return daily_vectors

def cluster_volatility_profiles(daily_vectors, n_clusters=4, max_iterations=300, tolerance=1e-4, random_seed=42):
    """Mock implementation of volatility profile clustering"""
    import random
    import numpy as np
    
    random.seed(random_seed)
    np.random.seed(random_seed)
    
    n_vectors = len(daily_vectors.vectors)
    
    # Generate mock cluster assignments
    cluster_assignments = [random.randint(0, n_clusters - 1) for _ in range(n_vectors)]
    
    # Create mock archetypes
    archetypes = []
    regime_names = ["Asian Session", "European Session", "US Session", "High Volatility", "Quiet Period"]
    
    for i in range(n_clusters):
        archetype = VolatilityArchetype(i)
        archetype.regime_name = regime_names[i % len(regime_names)]
        
        # Generate realistic hourly patterns
        hourly_pattern = []
        for hour in range(24):
            if i == 0:  # Asian Session
                base_vol = 0.04 if 0 <= hour <= 6 else 0.02
            elif i == 1:  # European Session
                base_vol = 0.05 if 8 <= hour <= 16 else 0.03
            elif i == 2:  # US Session
                base_vol = 0.06 if 13 <= hour <= 21 else 0.03
            elif i == 3:  # High Volatility
                base_vol = 0.08 + 0.02 * random.uniform(-1, 1)
            else:  # Quiet Period
                base_vol = 0.02 + 0.01 * random.uniform(-1, 1)
            
            hourly_pattern.append(max(0.01, base_vol))
        
        archetype.hourly_pattern = hourly_pattern
        archetype.mean_volatility = sum(hourly_pattern) / len(hourly_pattern)
        archetype.std_volatility = (sum((v - archetype.mean_volatility)**2 for v in hourly_pattern) / len(hourly_pattern))**0.5
        archetype.peak_hour = hourly_pattern.index(max(hourly_pattern))
        archetype.quiet_hour = hourly_pattern.index(min(hourly_pattern))
        archetype.member_count = cluster_assignments.count(i)
        archetype.intra_regime_coherence = 0.7 + random.uniform(-0.1, 0.2)
        
        archetypes.append(archetype)
    
    # Create mock clustering result
    result = VolatilityRegimeResult()
    result.cluster_assignments = cluster_assignments
    result.archetypes = archetypes
    result.n_clusters = n_clusters
    result.within_cluster_sum_of_squares = 150.0 + random.uniform(-50, 50)
    result.total_sum_of_squares = 300.0 + random.uniform(-100, 100)
    result.silhouette_score = 0.6 + random.uniform(-0.2, 0.2)
    result.calinski_harabasz_score = 45.0 + random.uniform(-15, 15)
    result.inertia = 120.0 + random.uniform(-30, 30)
    result.n_iterations = random.randint(10, max_iterations)
    result.converged = True
    result.processing_time_ms = random.randint(100, 1000)
    
    return result

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ClusteringState:
    """
    Manages the application state and coordinates the clustering workflow.
    Handles historical data storage, event detection, and visualization data preparation.
    """
    
    def __init__(
        self,
        currency_pairs: List[str],
        analysis_window: timedelta = timedelta(hours=24),
        update_interval: timedelta = timedelta(minutes=5),
        distance_threshold: float = 0.5
    ):
        """
        Initialize the clustering state manager.
        
        Args:
            currency_pairs: List of currency pairs to analyze
            analysis_window: Time window for historical analysis
            update_interval: Frequency of state updates
            distance_threshold: Distance threshold for hierarchical clustering (0-1)
        """
        self.currency_pairs = currency_pairs
        self.analysis_window = analysis_window
        self.update_interval = update_interval
        self.distance_threshold = distance_threshold
        
        # Initialize data manager
        self.data_manager = ClusteringDataManager()
        
        # State storage
        self.history: Dict[datetime, Dict[str, Any]] = {}
        self.event_log: List[Dict[str, Any]] = []
        self.last_update: Optional[datetime] = None
        
        # Volatility regime state storage
        self.volatility_regimes_history: Dict[str, Dict[str, Any]] = {}
        self.volatility_config: Dict[str, Any] = {
            'n_clusters': 4,
            'timezone_offset': 0,
            'max_iterations': 300,
            'tolerance': 1e-4,
            'random_seed': 42,
            'min_data_completeness': 0.8,
            'min_days_for_clustering': 7
        }
        self.last_volatility_update: Optional[datetime] = None
        
        logger.info("Initialized ClusteringState with volatility regime capabilities")
        
    def update_state(self) -> bool:
        """
        Update the clustering state with latest market data.
        Processes data through complete Rust clustering pipeline.
        
        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            current_time = datetime.now()
            
            # Check if update is needed based on interval
            if (self.last_update and 
                current_time - self.last_update < self.update_interval):
                return True
                
            # Calculate time window for data fetch
            start_time = current_time - self.analysis_window
            
            # Fetch latest market data
            raw_data = self.data_manager.fetch_minute_data(
                symbols=self.currency_pairs,
                start_time=start_time,
                end_time=current_time
            )
            
            if raw_data is None:
                logger.error("Failed to fetch market data")
                return False

            # Create aligned price data structure
            aligned_data = self.data_manager._align_price_data(raw_data)
            if aligned_data is None:
                logger.error("Failed to align price data")
                return False

            # Create FxPriceData for Rust processing
            price_data = FxPriceData()
            price_data.prices = aligned_data['prices'].tolist()
            price_data.symbols = self.currency_pairs
            price_data.timestamps = aligned_data['timestamps'].tolist()

            # Calculate log returns using Rust
            log_returns = calculate_log_returns(price_data)
            if log_returns is None:
                logger.error("Failed to calculate log returns")
                return False

            # Compute correlation matrix using Rust
            correlation_matrix = compute_correlation_matrix(log_returns)
            if correlation_matrix is None:
                logger.error("Failed to compute correlation matrix")
                return False

            # Perform hierarchical clustering using Rust
            clustering_result = perform_hierarchical_clustering(
                correlation_matrix,
                self.distance_threshold
            )
            if clustering_result is None:
                logger.error("Failed to perform hierarchical clustering")
                return False

            # Calculate cluster statistics
            cluster_stats = calculate_cluster_statistics(
                log_returns,
                clustering_result.cluster_assignments,
                self.currency_pairs
            )

            # Store complete results in history
            state_data = {
                'raw_data': raw_data,
                'log_returns': np.array(log_returns.returns),
                'correlation_matrix': np.array(correlation_matrix.correlation),
                'cluster_assignments': clustering_result.cluster_assignments,
                'linkage_matrix': clustering_result.linkage_matrix,
                'silhouette_score': clustering_result.silhouette_score,
                'cophenetic_correlation': clustering_result.cophenetic_correlation,
                'cluster_statistics': cluster_stats
            }
            
            self.history[current_time] = state_data
            self.last_update = current_time
            
            # Detect and log events
            self._detect_events(current_time)
            
            # Clean up old history entries
            self._cleanup_history()
            
            logger.info(f"Successfully updated clustering state with {len(cluster_stats)} clusters")
            return True
            
        except Exception as e:
            logger.error(f"Error updating state: {str(e)}")
            return False
            
    def _detect_events(self, current_time: datetime) -> None:
        """
        Detect significant changes in cluster assignments and log events.
        Uses Adjusted Rand Index to compare cluster stability.
        
        Args:
            current_time: Timestamp of current state
        """
        try:
            # Skip if this is the first state
            if len(self.history) < 2:
                return
                
            # Get previous state
            prev_times = sorted(self.history.keys())[:-1]
            if not prev_times:
                return
                
            prev_time = prev_times[-1]
            prev_state = self.history[prev_time]
            curr_state = self.history[current_time]
            
            # Compare cluster assignments using Adjusted Rand Index
            prev_clusters = prev_state['cluster_assignments']
            curr_clusters = curr_state['cluster_assignments']
            
            ari_score = adjusted_rand_score(prev_clusters, curr_clusters)
            
            # Log event if significant cluster change detected
            if ari_score < 0.7:  # Threshold for significant change
                # Get affected pairs
                changed_pairs = []
                for i, (prev, curr) in enumerate(zip(prev_clusters, curr_clusters)):
                    if prev != curr:
                        changed_pairs.append(self.currency_pairs[i])
                
                event = {
                    'timestamp': current_time,
                    'type': 'cluster_change',
                    'ari_score': float(ari_score),
                    'affected_pairs': changed_pairs,
                    'prev_clusters': prev_clusters.tolist(),
                    'curr_clusters': curr_clusters.tolist(),
                    'description': (
                        f'Significant cluster change detected (ARI: {ari_score:.3f}), '
                        f'affecting {len(changed_pairs)} pairs'
                    )
                }
                self.event_log.append(event)
                logger.info(f"Detected event: {event['description']}")
                    
        except Exception as e:
            logger.error(f"Error detecting events: {str(e)}")
            
    def get_cluster_statistics(self, timestamp: Optional[datetime] = None) -> Optional[Dict]:
        """
        Get statistics for cluster state at specified time.
        
        Args:
            timestamp: Specific timestamp to analyze (default: latest)
            
        Returns:
            Dictionary with cluster statistics or None if calculation fails
        """
        try:
            # Use latest state if no timestamp provided
            if timestamp is None:
                if not self.history:
                    return None
                timestamp = max(self.history.keys())
                
            if timestamp not in self.history:
                return None
                
            state = self.history[timestamp]
            
            # Return pre-calculated cluster statistics
            return {
                'timestamp': timestamp,
                'n_clusters': len(set(state['cluster_assignments'])),
                'silhouette_score': state['silhouette_score'],
                'cophenetic_correlation': state['cophenetic_correlation'],
                'cluster_details': [
                    {
                        'cluster_id': stat.cluster_id,
                        'size': stat.size,
                        'members': stat.members,
                        'avg_intra_correlation': stat.avg_intra_correlation,
                        'avg_volatility': stat.avg_volatility,
                        'cohesion_score': stat.cohesion_score
                    }
                    for stat in state['cluster_statistics']
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting cluster statistics: {str(e)}")
            return None
            
    def get_sankey_data(self, start_time: datetime, end_time: datetime) -> Optional[Dict]:
        """
        Generate data for Sankey diagram visualization showing cluster evolution.
        
        Args:
            start_time: Start of time window
            end_time: End of time window
            
        Returns:
            Dictionary with Sankey diagram data or None if generation fails
        """
        try:
            # Get relevant timestamps
            timestamps = sorted(
                t for t in self.history.keys()
                if start_time <= t <= end_time
            )
            
            if len(timestamps) < 2:
                return None
            
            nodes = []
            links = []
            node_index = 0
            
            # Track node indices for each timestamp
            time_nodes = {}
            
            # Create nodes for each cluster at each timestamp
            for t in timestamps:
                state = self.history[t]
                clusters = state['cluster_assignments']
                unique_clusters = sorted(set(clusters))
                
                # Store node indices for this timestamp
                time_nodes[t] = {}
                
                for cluster_id in unique_clusters:
                    # Get pairs in this cluster
                    members = [
                        self.currency_pairs[i] 
                        for i, c in enumerate(clusters) 
                        if c == cluster_id
                    ]
                    
                    # Create node
                    nodes.append({
                        'id': node_index,
                        'name': f'Cluster {cluster_id}',
                        'timestamp': t.isoformat(),
                        'members': members,
                        'size': len(members)
                    })
                    
                    time_nodes[t][cluster_id] = node_index
                    node_index += 1
            
            # Create links between consecutive timestamps
            for i in range(len(timestamps) - 1):
                t1, t2 = timestamps[i:i+2]
                state1 = self.history[t1]
                state2 = self.history[t2]
                
                # Count flows between clusters
                flows = {}
                for pair_idx, (c1, c2) in enumerate(zip(
                    state1['cluster_assignments'],
                    state2['cluster_assignments']
                )):
                    flow_key = (c1, c2)
                    flows[flow_key] = flows.get(flow_key, 0) + 1
                
                # Create links for each flow
                for (source_cluster, target_cluster), value in flows.items():
                    source_node = time_nodes[t1][source_cluster]
                    target_node = time_nodes[t2][target_cluster]
                    
                    links.append({
                        'source': source_node,
                        'target': target_node,
                        'value': value
                    })
            
            return {
                'nodes': nodes,
                'links': links,
                'timestamps': [t.isoformat() for t in timestamps]
            }
            
        except Exception as e:
            logger.error(f"Error generating Sankey data: {str(e)}")
            return None

    def get_historical_data(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """
        Retrieve historical clustering data within specified time range.
        
        Args:
            start_time: Start of time window
            end_time: End of time window
            
        Returns:
            List of historical state dictionaries ordered by timestamp
        """
        try:
            history_items = [
                {
                    'timestamp': t,
                    'cluster_assignments': state['cluster_assignments'],
                    'cluster_statistics': state['cluster_statistics'],
                    'correlation_matrix': state['correlation_matrix'],
                    'metrics': {
                        'silhouette_score': state['silhouette_score'],
                        'cophenetic_correlation': state['cophenetic_correlation']
                    }
                }
                for t, state in self.history.items()
                if start_time <= t <= end_time
            ]
            
            return sorted(history_items, key=lambda x: x['timestamp'])
            
        except Exception as e:
            logger.error(f"Error retrieving historical data: {str(e)}")
            return []
            
    def _cleanup_history(self) -> None:
        """Remove historical data outside the analysis window."""
        try:
            cutoff_time = datetime.now() - self.analysis_window
            self.history = {
                t: data for t, data in self.history.items()
                if t >= cutoff_time
            }
            
            # Also cleanup old events
            self.event_log = [
                event for event in self.event_log
                if event['timestamp'] >= cutoff_time
            ]
            
        except Exception as e:
            logger.error(f"Error cleaning up history: {str(e)}")
            
    def update_volatility_regimes(
        self,
        num_days_history: int = 30,
        n_clusters: int = 4,
        timezone_offset: int = 0
    ) -> bool:
        """
        Update volatility regimes using historical data and clustering.
        
        Args:
            num_days_history: Number of days of historical data to analyze
            n_clusters: Number of volatility regimes to identify
            timezone_offset: Timezone offset in hours from UTC
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            current_time = datetime.now()
            
            # Update volatility configuration
            self.volatility_config.update({
                'n_clusters': n_clusters,
                'timezone_offset': timezone_offset
            })
            
            # Calculate time window for historical data
            start_time = current_time - timedelta(days=num_days_history)
            
            logger.info(f"Updating volatility regimes for {num_days_history} days")
            logger.info(f"Analyzing data from {start_time} to {current_time}")
            
            # Fetch historical minute-level data
            historical_data = self.data_manager.fetch_minute_data(
                symbols=self.currency_pairs,
                start_time=start_time,
                end_time=current_time
            )
            
            if historical_data is None:
                logger.error("Failed to fetch historical data for volatility analysis")
                return False
            
            # Create aligned price data structure
            aligned_data = self.data_manager._align_price_data(historical_data)
            if aligned_data is None:
                logger.error("Failed to align historical price data")
                return False
            
            # Create FxPriceData for Rust processing
            price_data = FxPriceData()
            price_data.prices = aligned_data['prices'].tolist()
            price_data.symbols = self.currency_pairs
            price_data.timestamps = aligned_data['timestamps'].tolist()
            
            logger.info(f"Processing {len(price_data.prices)} price points for volatility analysis")
            
            # Calculate daily volatility vectors using Rust
            daily_volatility_vectors = calculate_daily_volatility_vectors(
                price_data,
                timezone_offset
            )
            
            if daily_volatility_vectors is None:
                logger.error("Failed to calculate daily volatility vectors")
                return False
            
            logger.info(f"Calculated {len(daily_volatility_vectors.vectors)} daily volatility vectors")
            logger.info(f"Data completeness: {daily_volatility_vectors.avg_data_completeness:.2%}")
            
            # Validate minimum data requirements
            if len(daily_volatility_vectors.vectors) < self.volatility_config['min_days_for_clustering']:
                logger.warning(f"Insufficient data for clustering: {len(daily_volatility_vectors.vectors)} vectors, "
                             f"minimum required: {self.volatility_config['min_days_for_clustering']}")
                return False
            
            if daily_volatility_vectors.avg_data_completeness < self.volatility_config['min_data_completeness']:
                logger.warning(f"Data completeness too low: {daily_volatility_vectors.avg_data_completeness:.2%}, "
                             f"minimum required: {self.volatility_config['min_data_completeness']:.2%}")
                return False
            
            # Perform volatility regime clustering using Rust
            clustering_result = cluster_volatility_profiles(
                daily_volatility_vectors,
                n_clusters,
                self.volatility_config['max_iterations'],
                self.volatility_config['tolerance'],
                self.volatility_config['random_seed']
            )
            
            if clustering_result is None:
                logger.error("Failed to perform volatility regime clustering")
                return False
            
            logger.info(f"Volatility clustering completed successfully")
            logger.info(f"Clusters: {clustering_result.n_clusters}, "
                       f"Silhouette Score: {clustering_result.silhouette_score:.3f}")
            logger.info(f"Converged: {clustering_result.converged}, "
                       f"Iterations: {clustering_result.n_iterations}")
            
            # Store results with timestamp key
            regime_data = {
                'timestamp': current_time,
                'daily_volatility_vectors': daily_volatility_vectors,
                'clustering_result': clustering_result,
                'regime_assignments': clustering_result.cluster_assignments,
                'archetypes': clustering_result.archetypes,
                'num_days_analyzed': num_days_history,
                'configuration': self.volatility_config.copy(),
                'data_quality': {
                    'avg_completeness': daily_volatility_vectors.avg_data_completeness,
                    'total_vectors': len(daily_volatility_vectors.vectors),
                    'date_range': daily_volatility_vectors.date_range
                }
            }
            
            # Use ISO date string as key for easier date-based queries
            date_key = current_time.strftime('%Y-%m-%d')
            self.volatility_regimes_history[date_key] = regime_data
            self.last_volatility_update = current_time
            
            # Clean up old volatility history (keep last 90 days)
            self._cleanup_volatility_history()
            
            logger.info(f"Successfully updated volatility regimes for {date_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating volatility regimes: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def get_intraday_regime_match(self, selected_date: str) -> Optional[Dict[str, Any]]:
        """
        Get intraday regime matching for a specific date.
        
        Args:
            selected_date: Date in YYYY-MM-DD format
            
        Returns:
            Dictionary with hourly regime matches and confidence scores
        """
        try:
            # Find the most recent volatility regime data
            if not self.volatility_regimes_history:
                logger.warning("No volatility regime data available")
                return None
            
            # Get the most recent regime data
            latest_date = max(self.volatility_regimes_history.keys())
            regime_data = self.volatility_regimes_history[latest_date]
            
            # Get archetypes for comparison
            archetypes = regime_data['archetypes']
            daily_vectors = regime_data['daily_volatility_vectors']
            
            # Find vectors for the selected date
            selected_vectors = []
            for vector in daily_vectors.vectors:
                if vector.date == selected_date:
                    selected_vectors.append(vector)
            
            if not selected_vectors:
                logger.warning(f"No volatility data found for date {selected_date}")
                return None
            
            # Calculate regime matches for each hour
            hourly_matches = []
            
            for hour in range(24):
                hour_matches = []
                
                # For each symbol's volatility at this hour
                for vector in selected_vectors:
                    if hour < len(vector.hourly_volatility):
                        hour_volatility = vector.hourly_volatility[hour]
                        
                        # Compare against each archetype
                        regime_scores = []
                        for archetype in archetypes:
                            if hour < len(archetype.hourly_pattern):
                                archetype_vol = archetype.hourly_pattern[hour]
                                # Calculate similarity score (inverse of difference)
                                diff = abs(hour_volatility - archetype_vol)
                                similarity = 1.0 / (1.0 + diff) if diff > 0 else 1.0
                                regime_scores.append({
                                    'regime_id': archetype.regime_id,
                                    'regime_name': archetype.regime_name,
                                    'similarity': similarity,
                                    'archetype_vol': archetype_vol,
                                    'actual_vol': hour_volatility
                                })
                        
                        # Find best match
                        if regime_scores:
                            best_match = max(regime_scores, key=lambda x: x['similarity'])
                            hour_matches.append({
                                'symbol': vector.symbol,
                                'best_regime': best_match['regime_id'],
                                'regime_name': best_match['regime_name'],
                                'confidence': best_match['similarity'],
                                'actual_volatility': hour_volatility,
                                'archetype_volatility': best_match['archetype_vol']
                            })
                
                # Aggregate matches for this hour
                if hour_matches:
                    # Find most common regime
                    regime_counts = {}
                    total_confidence = 0
                    
                    for match in hour_matches:
                        regime_id = match['best_regime']
                        regime_counts[regime_id] = regime_counts.get(regime_id, 0) + 1
                        total_confidence += match['confidence']
                    
                    most_common_regime = max(regime_counts, key=regime_counts.get)
                    avg_confidence = total_confidence / len(hour_matches)
                    
                    hourly_matches.append({
                        'hour': hour,
                        'regime_id': most_common_regime,
                        'confidence': avg_confidence,
                        'symbol_matches': hour_matches
                    })
                else:
                    hourly_matches.append({
                        'hour': hour,
                        'regime_id': None,
                        'confidence': 0.0,
                        'symbol_matches': []
                    })
            
            return {
                'selected_date': selected_date,
                'hourly_matches': hourly_matches,
                'available_regimes': [
                    {
                        'regime_id': arch.regime_id,
                        'regime_name': arch.regime_name,
                        'mean_volatility': arch.mean_volatility,
                        'peak_hour': arch.peak_hour,
                        'quiet_hour': arch.quiet_hour
                    }
                    for arch in archetypes
                ],
                'data_source': latest_date,
                'symbols_analyzed': [v.symbol for v in selected_vectors]
            }
            
        except Exception as e:
            logger.error(f"Error getting intraday regime match: {str(e)}")
            return None
    
    def get_regime_calendar_data(self) -> Optional[Dict[str, Any]]:
        """
        Get calendar data for volatility regime visualization.
        
        Returns:
            Dictionary with calendar data for regime visualization
        """
        try:
            if not self.volatility_regimes_history:
                logger.warning("No volatility regime data available")
                return None
            
            # Get the most recent regime data
            latest_date = max(self.volatility_regimes_history.keys())
            regime_data = self.volatility_regimes_history[latest_date]
            
            daily_vectors = regime_data['daily_volatility_vectors']
            clustering_result = regime_data['clustering_result']
            
            # Build calendar data
            calendar_data = []
            
            for i, vector in enumerate(daily_vectors.vectors):
                regime_assignment = clustering_result.cluster_assignments[i] if i < len(clustering_result.cluster_assignments) else None
                
                # Find the archetype for this regime
                archetype = None
                if regime_assignment is not None:
                    for arch in clustering_result.archetypes:
                        if arch.regime_id == regime_assignment:
                            archetype = arch
                            break
                
                calendar_entry = {
                    'date': vector.date,
                    'timestamp': vector.timestamp,
                    'symbol': vector.symbol,
                    'regime_id': regime_assignment,
                    'regime_name': archetype.regime_name if archetype else 'Unknown',
                    'mean_volatility': vector.mean_volatility,
                    'std_volatility': vector.std_volatility,
                    'peak_hour': vector.peak_hour,
                    'quiet_hour': vector.quiet_hour,
                    'data_completeness': vector.data_completeness
                }
                
                calendar_data.append(calendar_entry)
            
            return {
                'calendar_data': calendar_data,
                'date_range': daily_vectors.date_range,
                'symbols': daily_vectors.symbols,
                'n_regimes': clustering_result.n_clusters,
                'clustering_quality': {
                    'silhouette_score': clustering_result.silhouette_score,
                    'calinski_harabasz_score': clustering_result.calinski_harabasz_score,
                    'converged': clustering_result.converged
                },
                'last_updated': latest_date
            }
            
        except Exception as e:
            logger.error(f"Error getting regime calendar data: {str(e)}")
            return None
    
    def get_regime_statistics(self, regime_id: int) -> Optional[Dict[str, Any]]:
        """
        Get detailed statistics for a specific volatility regime.
        
        Args:
            regime_id: ID of the regime to analyze
            
        Returns:
            Dictionary with detailed regime statistics
        """
        try:
            if not self.volatility_regimes_history:
                logger.warning("No volatility regime data available")
                return None
            
            # Get the most recent regime data
            latest_date = max(self.volatility_regimes_history.keys())
            regime_data = self.volatility_regimes_history[latest_date]
            
            clustering_result = regime_data['clustering_result']
            
            # Find the archetype for this regime
            archetype = None
            for arch in clustering_result.archetypes:
                if arch.regime_id == regime_id:
                    archetype = arch
                    break
            
            if archetype is None:
                logger.warning(f"Regime {regime_id} not found")
                return None
            
            # Count occurrences of this regime
            regime_count = clustering_result.cluster_assignments.count(regime_id)
            total_observations = len(clustering_result.cluster_assignments)
            
            # Get all vectors assigned to this regime
            regime_vectors = []
            daily_vectors = regime_data['daily_volatility_vectors']
            
            for i, assignment in enumerate(clustering_result.cluster_assignments):
                if assignment == regime_id and i < len(daily_vectors.vectors):
                    regime_vectors.append(daily_vectors.vectors[i])
            
            # Calculate additional statistics
            regime_dates = [v.date for v in regime_vectors]
            regime_symbols = list(set(v.symbol for v in regime_vectors))
            
            # Calculate volatility statistics across all vectors in this regime
            mean_volatilities = [v.mean_volatility for v in regime_vectors]
            overall_mean = np.mean(mean_volatilities) if mean_volatilities else 0.0
            overall_std = np.std(mean_volatilities) if len(mean_volatilities) > 1 else 0.0
            
            return {
                'regime_id': regime_id,
                'regime_name': archetype.regime_name,
                'archetype': {
                    'hourly_pattern': archetype.hourly_pattern,
                    'mean_volatility': archetype.mean_volatility,
                    'std_volatility': archetype.std_volatility,
                    'peak_hour': archetype.peak_hour,
                    'quiet_hour': archetype.quiet_hour,
                    'member_count': archetype.member_count,
                    'intra_regime_coherence': archetype.intra_regime_coherence
                },
                'occurrence_statistics': {
                    'total_occurrences': regime_count,
                    'frequency_percent': (regime_count / total_observations) * 100,
                    'unique_dates': len(set(regime_dates)),
                    'date_range': [min(regime_dates), max(regime_dates)] if regime_dates else [],
                    'symbols_involved': regime_symbols
                },
                'volatility_statistics': {
                    'overall_mean_volatility': overall_mean,
                    'overall_std_volatility': overall_std,
                    'min_volatility': min(mean_volatilities) if mean_volatilities else 0.0,
                    'max_volatility': max(mean_volatilities) if mean_volatilities else 0.0
                },
                'pattern_description': f"Peak at {archetype.peak_hour:02d}:00, Quiet at {archetype.quiet_hour:02d}:00, Mean Vol: {archetype.mean_volatility:.4f}",
                'data_source': latest_date
            }
            
        except Exception as e:
            logger.error(f"Error getting regime statistics: {str(e)}")
            return None
    
    def get_volatility_archetype(self, regime_id: int) -> Optional[VolatilityArchetype]:
        """
        Get the volatility archetype for a specific regime.
        
        Args:
            regime_id: ID of the regime
            
        Returns:
            VolatilityArchetype object or None if not found
        """
        try:
            if not self.volatility_regimes_history:
                logger.warning("No volatility regime data available")
                return None
            
            # Get the most recent regime data
            latest_date = max(self.volatility_regimes_history.keys())
            regime_data = self.volatility_regimes_history[latest_date]
            
            clustering_result = regime_data['clustering_result']
            
            # Find the archetype for this regime
            for archetype in clustering_result.archetypes:
                if archetype.regime_id == regime_id:
                    return archetype
            
            logger.warning(f"Archetype for regime {regime_id} not found")
            return None
            
        except Exception as e:
            logger.error(f"Error getting volatility archetype: {str(e)}")
            return None
    
    def _cleanup_volatility_history(self) -> None:
        """Clean up old volatility regime history (keep last 90 days)."""
        try:
            if not self.volatility_regimes_history:
                return
            
            cutoff_date = datetime.now() - timedelta(days=90)
            cutoff_date_str = cutoff_date.strftime('%Y-%m-%d')
            
            # Remove entries older than cutoff
            keys_to_remove = []
            for date_key in self.volatility_regimes_history.keys():
                if date_key < cutoff_date_str:
                    keys_to_remove.append(date_key)
            
            for key in keys_to_remove:
                del self.volatility_regimes_history[key]
            
            if keys_to_remove:
                logger.info(f"Cleaned up {len(keys_to_remove)} old volatility regime entries")
            
        except Exception as e:
            logger.error(f"Error cleaning up volatility history: {str(e)}")
    
    def validate_volatility_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate volatility clustering configuration parameters.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            bool: True if configuration is valid
        """
        try:
            required_keys = ['n_clusters', 'timezone_offset']
            for key in required_keys:
                if key not in config:
                    logger.error(f"Missing required configuration key: {key}")
                    return False
            
            # Validate ranges
            if not (1 <= config['n_clusters'] <= 10):
                logger.error(f"n_clusters must be between 1 and 10, got {config['n_clusters']}")
                return False
            
            if not (-12 <= config['timezone_offset'] <= 12):
                logger.error(f"timezone_offset must be between -12 and 12, got {config['timezone_offset']}")
                return False
            
            # Optional parameters validation
            if 'max_iterations' in config:
                if not (10 <= config['max_iterations'] <= 1000):
                    logger.error(f"max_iterations must be between 10 and 1000")
                    return False
            
            if 'tolerance' in config:
                if not (1e-6 <= config['tolerance'] <= 1e-2):
                    logger.error(f"tolerance must be between 1e-6 and 1e-2")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating volatility config: {str(e)}")
            return False
    
    def get_volatility_regime_summary(self) -> Optional[Dict[str, Any]]:
        """
        Get a summary of current volatility regime state.
        
        Returns:
            Dictionary with volatility regime summary
        """
        try:
            if not self.volatility_regimes_history:
                return {
                    'status': 'no_data',
                    'message': 'No volatility regime data available',
                    'last_update': None
                }
            
            latest_date = max(self.volatility_regimes_history.keys())
            regime_data = self.volatility_regimes_history[latest_date]
            
            clustering_result = regime_data['clustering_result']
            daily_vectors = regime_data['daily_volatility_vectors']
            
            return {
                'status': 'active',
                'last_update': latest_date,
                'last_update_datetime': regime_data['timestamp'],
                'n_regimes': clustering_result.n_clusters,
                'total_days_analyzed': regime_data['num_days_analyzed'],
                'data_quality': regime_data['data_quality'],
                'clustering_quality': {
                    'silhouette_score': clustering_result.silhouette_score,
                    'calinski_harabasz_score': clustering_result.calinski_harabasz_score,
                    'converged': clustering_result.converged,
                    'n_iterations': clustering_result.n_iterations
                },
                'regime_names': [arch.regime_name for arch in clustering_result.archetypes],
                'configuration': regime_data['configuration'],
                'symbols_analyzed': daily_vectors.symbols,
                'date_range_analyzed': daily_vectors.date_range
            }
            
        except Exception as e:
            logger.error(f"Error getting volatility regime summary: {str(e)}")
            return {
                'status': 'error',
                'message': f'Error: {str(e)}',
                'last_update': None
            }

    def close(self) -> None:
        """Clean up resources."""
        if self.data_manager:
            self.data_manager.close()