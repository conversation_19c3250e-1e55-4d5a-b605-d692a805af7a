{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 17212183120498275042, "deps": [[1988483478007900009, "unicode_ident", false, 13329671588530556275], [3060637413840920116, "proc_macro2", false, 9742792036988073286], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-1ee8e139f52c1670\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}