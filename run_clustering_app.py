import dash
from dash import dcc, html, callback_context, Input, Output, State, dependencies
import dash_bootstrap_components as dbc
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.figure_factory as ff
import numpy as np

from clustering.state_manager import ClusteringState
from weekend_utils import get_weekend_status_message, is_weekend

# Initialize the app with a professional Bootstrap theme
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.FLATLY],
    title="FX Correlation Clustering"
)

# Import all 28 currency pairs from config
from config import CURRENCY_PAIRS

# Helper function to convert MT5 format to display format
def mt5_to_display_format(symbol):
    """Convert MT5 format (EURUSD) to display format (EUR/USD)"""
    if len(symbol) == 6:
        return f"{symbol[:3]}/{symbol[3:]}"
    return symbol

# Use MT5 format for data fetching, convert to display format only for UI
state_manager = ClusteringState(
    currency_pairs=CURRENCY_PAIRS,  # Use MT5 format (EURUSD, GBPUSD, etc.)
    analysis_window=timedelta(hours=24),
    update_interval=timedelta(minutes=5),
    distance_threshold=0.5
)

# Create placeholder figures
def create_placeholder_figure(title):
    return go.Figure().update_layout(
        title=title,
        xaxis_visible=False,
        yaxis_visible=False,
        annotations=[{
            "text": "Loading data...",
            "xref": "paper",
            "yref": "paper",
            "showarrow": False,
            "font": {"size": 20}
        }]
    )

# Define color scheme for consistency
COLOR_SCHEME = [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
]

# Helper functions for time scrubber
def timestamp_to_slider_value(timestamp, timestamps):
    """Convert timestamp to slider numerical value"""
    if not timestamps:
        return 0
    try:
        return timestamps.index(timestamp)
    except ValueError:
        return len(timestamps) - 1

def slider_value_to_timestamp(slider_value, timestamps):
    """Convert slider value back to timestamp"""
    if not timestamps or slider_value is None:
        return None
    try:
        idx = int(slider_value)
        if 0 <= idx < len(timestamps):
            return timestamps[idx]
        return timestamps[-1] if timestamps else None
    except (ValueError, IndexError):
        return timestamps[-1] if timestamps else None

def generate_time_marks(timestamps):
    """Create appropriate time marks for the slider"""
    if not timestamps:
        return {}
    
    marks = {}
    n_timestamps = len(timestamps)
    
    # Generate marks based on data density
    if n_timestamps <= 10:
        # Show all timestamps for small datasets
        for i, ts in enumerate(timestamps):
            marks[i] = {
                'label': ts.strftime('%H:%M'),
                'style': {'font-size': '10px', 'transform': 'rotate(-45deg)'}
            }
    elif n_timestamps <= 50:
        # Show every 5th timestamp for medium datasets
        for i in range(0, n_timestamps, max(1, n_timestamps // 10)):
            marks[i] = {
                'label': timestamps[i].strftime('%H:%M'),
                'style': {'font-size': '10px', 'transform': 'rotate(-45deg)'}
            }
    else:
        # Show hourly marks for large datasets
        step = max(1, n_timestamps // 8)
        for i in range(0, n_timestamps, step):
            marks[i] = {
                'label': timestamps[i].strftime('%H:%M'),
                'style': {'font-size': '10px', 'transform': 'rotate(-45deg)'}
            }
    
    return marks

def format_timestamp_display(timestamp):
    """Format timestamp for user display"""
    if timestamp is None:
        return "No data available"
    return timestamp.strftime('%Y-%m-%d %H:%M:%S')

# Layout components
time_controls = dbc.Card([
    dbc.CardBody([
        dbc.Row([
            dbc.Col([
                html.Label("Time Navigation", className="form-label small fw-bold"),
                dcc.Slider(
                    id='time-slider',
                    min=0,
                    max=100,
                    step=1,
                    value=100,
                    marks=None,
                    tooltip={"placement": "bottom", "always_visible": True}
                )
            ], width=8),
            dbc.Col([
                html.Label("Controls", className="form-label small fw-bold"),
                dbc.ButtonGroup([
                    dbc.Button(
                        [html.I(className="fas fa-play me-1"), "Play"],
                        id="play-button",
                        size="sm",
                        color="primary",
                        outline=True
                    ),
                    dbc.Button(
                        [html.I(className="fas fa-broadcast-tower me-1"), "Live"],
                        id="live-button",
                        size="sm",
                        color="success",
                        outline=True
                    )
                ], className="d-grid gap-1")
            ], width=4)
        ]),
        html.Div(id='time-display', className="text-center mt-2 small text-muted")
    ])
], className="mb-4")

# Define the regime color scheme
REGIME_COLORS = {
    0: '#1f77b4',  # Blue - Asian Session
    1: '#ff7f0e',  # Orange - European Session
    2: '#2ca02c',  # Green - US Session
    3: '#d62728',  # Red - High Volatility
    4: '#9467bd',  # Purple - Quiet Period
    5: '#8c564b',  # Brown - Market Open
    6: '#e377c2',  # Pink - Market Close
    7: '#7f7f7f',  # Gray - Weekend/Holiday
}

# Define the tabbed layout
app.layout = dbc.Container([
    # Hidden components for state management
    dcc.Store(id='cluster-data-store'),
    dcc.Store(id='selected-cluster-store'),
    dcc.Store(id='play-state-store', data={'playing': False}),
    dcc.Store(id='regime-config-store', data={'n_clusters': 4, 'timezone_offset': 0}),
    dcc.Interval(id='update-interval', interval=30*1000),  # 30 seconds
    dcc.Interval(id='play-interval', interval=1000, disabled=True),  # 1 second for play mode
    dcc.Interval(id='regime-update-interval', interval=300*1000),  # 5 minutes for regime updates
    
    # Title row
    dbc.Row([
        dbc.Col([
            html.H1("Dynamic FX Clustering Application", className="text-primary text-center my-4")
        ])
    ]),

    # Weekend status row
    dbc.Row([
        dbc.Col([
            html.Div(id="weekend-status-display", className="text-center mb-3")
        ])
    ]),
    
    # Main tabbed interface
    dcc.Tabs(id="main-tabs", value="correlation-tab", className="mb-4", children=[
        # Correlation Clustering Tab
        dcc.Tab(label="Correlation Clustering", value="correlation-tab", className="custom-tab", children=[
            # Time control row
            dbc.Row([
                dbc.Col([time_controls])
            ], className="mb-4"),
            
            # Top panels row
            dbc.Row([
                # Dendrogram panel
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H4("Currency Correlation Dendrogram", className="m-0")
                        ]),
                        dbc.CardBody([
                            dcc.Loading(
                                id="dendrogram-loading",
                                type="default",
                                children=dcc.Graph(
                                    id='dendrogram-graph',
                                    figure=create_placeholder_figure("Currency Correlation Dendrogram"),
                                    config={'displayModeBar': True}
                                )
                            )
                        ])
                    ], className="h-100")
                ], width=6),
                
                # Sankey diagram panel
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H4("Cluster Evolution Timeline", className="m-0")
                        ]),
                        dbc.CardBody([
                            dcc.Loading(
                                id="sankey-loading",
                                type="default",
                                children=dcc.Graph(
                                    id='sankey-graph',
                                    figure=create_placeholder_figure("Cluster Evolution Timeline"),
                                    config={'displayModeBar': True}
                                )
                            )
                        ])
                    ], className="h-100")
                ], width=6)
            ], className="mb-4"),
            
            # Bottom panels row
            dbc.Row([
                # Statistics panel
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H4("Cluster Statistics", className="m-0")
                        ]),
                        dbc.CardBody([
                            dcc.Loading(
                                id="stats-loading",
                                type="default",
                                children=html.Div(id='stats-content', className="stats-panel")
                            )
                        ])
                    ], className="h-100")
                ], width=6),
                
                # Event log panel
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H4("Cluster Events", className="m-0")
                        ]),
                        dbc.CardBody([
                            dcc.Loading(
                                id="events-loading",
                                type="default",
                                children=html.Div(id='events-content', className="events-panel")
                            )
                        ])
                    ], className="h-100")
                ], width=6)
            ])
        ]),
        
        # Volatility Regimes Tab
        dcc.Tab(label="Volatility Regimes", value="regimes-tab", className="custom-tab", children=[
            # Regime controls row
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H4("Regime Analysis Controls", className="m-0")
                        ]),
                        dbc.CardBody([
                            dbc.Row([
                                dbc.Col([
                                    html.Label("Number of Clusters", className="form-label small fw-bold"),
                                    dcc.Slider(
                                        id='n-clusters-slider',
                                        min=2,
                                        max=8,
                                        step=1,
                                        value=4,
                                        marks={i: str(i) for i in range(2, 9)},
                                        tooltip={"placement": "bottom", "always_visible": True}
                                    )
                                ], width=4),
                                dbc.Col([
                                    html.Label("Timezone Offset (hours)", className="form-label small fw-bold"),
                                    dcc.Dropdown(
                                        id='timezone-dropdown',
                                        options=[
                                            {'label': 'UTC+0 (London)', 'value': 0},
                                            {'label': 'UTC+1 (Central Europe)', 'value': 1},
                                            {'label': 'UTC+2 (Eastern Europe)', 'value': 2},
                                            {'label': 'UTC+3 (Moscow)', 'value': 3},
                                            {'label': 'UTC-5 (New York)', 'value': -5},
                                            {'label': 'UTC-8 (Pacific)', 'value': -8},
                                            {'label': 'UTC+9 (Tokyo)', 'value': 9},
                                            {'label': 'UTC+10 (Sydney)', 'value': 10}
                                        ],
                                        value=0,
                                        clearable=False
                                    )
                                ], width=4),
                                dbc.Col([
                                    html.Label("Analysis Period", className="form-label small fw-bold"),
                                    dcc.Dropdown(
                                        id='analysis-period-dropdown',
                                        options=[
                                            {'label': '7 days', 'value': 7},
                                            {'label': '14 days', 'value': 14},
                                            {'label': '30 days', 'value': 30},
                                            {'label': '60 days', 'value': 60},
                                            {'label': '90 days', 'value': 90}
                                        ],
                                        value=30,
                                        clearable=False
                                    )
                                ], width=3),
                                dbc.Col([
                                    html.Label("Action", className="form-label small fw-bold"),
                                    dbc.Button(
                                        [html.I(className="fas fa-sync-alt me-2"), "Update Regimes"],
                                        id="update-regimes-button",
                                        color="primary",
                                        size="sm",
                                        className="w-100"
                                    )
                                ], width=1)
                            ])
                        ])
                    ], className="mb-4")
                ])
            ]),
            
            # Main regime content row
            dbc.Row([
                # Calendar panel
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H4("Volatility Regime Calendar", className="m-0")
                        ]),
                        dbc.CardBody([
                            dcc.Loading(
                                id="regime-calendar-loading",
                                type="default",
                                children=html.Div(id='regime-calendar-content', className="regime-calendar-panel")
                            )
                        ])
                    ], className="h-100")
                ], width=8),
                
                # Regime legend and statistics panel
                dbc.Col([
                    # Regime legend
                    dbc.Card([
                        dbc.CardHeader([
                            html.H5("Regime Legend", className="m-0")
                        ]),
                        dbc.CardBody([
                            html.Div(id='regime-legend-content', className="regime-legend-panel")
                        ])
                    ], className="mb-3"),
                    
                    # Summary statistics
                    dbc.Card([
                        dbc.CardHeader([
                            html.H5("Summary Statistics", className="m-0")
                        ]),
                        dbc.CardBody([
                            dcc.Loading(
                                id="regime-stats-loading",
                                type="default",
                                children=html.Div(id='regime-stats-content', className="regime-stats-panel")
                            )
                        ])
                    ])
                ], width=4)
            ])
        ])
    ]),
    
    # "Before and After" Modal for event details
    dbc.Modal([
        dbc.ModalHeader([
            dbc.ModalTitle("Event Details - Before and After Comparison", id="modal-title")
        ]),
        dbc.ModalBody([
            dcc.Loading(
                id="modal-loading",
                type="default",
                children=html.Div(id="modal-content")
            )
        ]),
        dbc.ModalFooter([
            dbc.Button(
                "Close",
                id="modal-close-button",
                className="ms-auto",
                color="secondary",
                n_clicks=0
            )
        ])
    ], id="event-modal", size="xl", scrollable=True, is_open=False),
    
    # Daily Drill-Down Modal for volatility analysis
    dbc.Modal([
        dbc.ModalHeader([
            dbc.ModalTitle("Daily Volatility Analysis", id="daily-modal-title")
        ]),
        dbc.ModalBody([
            dcc.Loading(
                id="daily-modal-loading",
                type="default",
                children=html.Div(id="daily-modal-content")
            )
        ]),
        dbc.ModalFooter([
            html.Div(id="daily-modal-footer"),
            dbc.Button(
                "Close",
                id="daily-modal-close-button",
                className="ms-auto",
                color="secondary",
                n_clicks=0
            )
        ])
    ], id="daily-drill-modal", size="xl", scrollable=True, is_open=False),
    
    # Store for selected date
    dcc.Store(id='selected-date-store', data=None)
    
], fluid=True, className="p-4")

# Time Scrubber Slider callback
@app.callback(
    [
        dash.Output('time-slider', 'min'),
        dash.Output('time-slider', 'max'),
        dash.Output('time-slider', 'marks'),
        dash.Output('time-slider', 'value'),
        dash.Output('time-display', 'children')
    ],
    [
        dash.Input('update-interval', 'n_intervals'),
        dash.Input('live-button', 'n_clicks'),
        dash.Input('play-button', 'n_clicks')
    ],
    [
        dash.State('time-slider', 'value'),
        dash.State('live-button', 'outline'),
        dash.State('play-button', 'outline')
    ]
)
def update_time_scrubber(n_intervals, live_clicks, play_clicks, current_slider_value,
                        live_outline, play_outline):
    """Update time scrubber slider properties based on available history"""
    try:
        # Get available timestamps from history
        timestamps = sorted(state_manager.history.keys())
        
        if not timestamps:
            return 0, 0, {}, 0, "No historical data available"
        
        # Generate slider properties
        min_val = 0
        max_val = len(timestamps) - 1
        marks = generate_time_marks(timestamps)
        
        # Determine current value based on context
        ctx = dash.callback_context
        if ctx.triggered:
            trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
            
            if trigger_id == 'live-button':
                # Live mode - set to most recent
                current_value = max_val
            elif trigger_id == 'play-button':
                # Play mode - advance by one step
                current_value = min(current_slider_value + 1, max_val) if current_slider_value is not None else 0
            else:
                # Regular update - keep current value or set to latest
                current_value = current_slider_value if current_slider_value is not None else max_val
        else:
            # Initial load - set to most recent
            current_value = max_val
        
        # Ensure current_value is within bounds
        current_value = max(min_val, min(current_value, max_val))
        
        # Get timestamp for display
        selected_timestamp = slider_value_to_timestamp(current_value, timestamps)
        display_text = format_timestamp_display(selected_timestamp)
        
        if len(timestamps) > 1:
            display_text += f" (Data point {current_value + 1} of {len(timestamps)})"
        
        return min_val, max_val, marks, current_value, display_text
        
    except Exception as e:
        print(f"Error updating time scrubber: {str(e)}")
        return 0, 0, {}, 0, "Error updating time controls"

# Play button callback
@app.callback(
    [
        dash.Output('play-interval', 'disabled'),
        dash.Output('play-button', 'children'),
        dash.Output('play-button', 'color'),
        dash.Output('play-state-store', 'data')
    ],
    [
        dash.Input('play-button', 'n_clicks'),
        dash.Input('time-slider', 'value')
    ],
    [
        dash.State('play-state-store', 'data'),
        dash.State('time-slider', 'max')
    ]
)
def handle_play_button(n_clicks, slider_value, play_state, slider_max):
    """Handle play button toggle and auto-advance functionality"""
    try:
        if n_clicks is None:
            return True, [html.I(className="fas fa-play me-1"), "Play"], "primary", {'playing': False}
        
        # Toggle play state
        is_playing = not play_state.get('playing', False)
        
        # Check if we're at the end - if so, reset to beginning
        if is_playing and slider_value >= slider_max:
            slider_value = 0
        
        # Update button appearance based on state
        if is_playing:
            button_content = [html.I(className="fas fa-pause me-1"), "Pause"]
            button_color = "warning"
            interval_disabled = False
        else:
            button_content = [html.I(className="fas fa-play me-1"), "Play"]
            button_color = "primary"
            interval_disabled = True
        
        return interval_disabled, button_content, button_color, {'playing': is_playing}
        
    except Exception as e:
        print(f"Error handling play button: {str(e)}")
        return True, [html.I(className="fas fa-play me-1"), "Play"], "primary", {'playing': False}

# Play interval callback for auto-advance
@app.callback(
    dash.Output('time-slider', 'value', allow_duplicate=True),
    [dash.Input('play-interval', 'n_intervals')],
    [
        dash.State('time-slider', 'value'),
        dash.State('time-slider', 'max'),
        dash.State('play-state-store', 'data')
    ],
    prevent_initial_call=True
)
def auto_advance_slider(n_intervals, current_value, max_value, play_state):
    """Auto-advance slider when in play mode"""
    try:
        if not play_state.get('playing', False):
            return dash.no_update
        
        if current_value is None:
            return 0
        
        # Advance to next position
        next_value = current_value + 1
        
        # Loop back to start if at end
        if next_value > max_value:
            next_value = 0
        
        return next_value
        
    except Exception as e:
        print(f"Error auto-advancing slider: {str(e)}")
        return dash.no_update

    except Exception as e:
        print(f"Error updating time scrubber: {str(e)}")
        return 0, 0, {}, 0, "Error updating time controls"

# Main callback for periodic updates
@app.callback(
    [dash.Output('cluster-data-store', 'data')],
    [dash.Input('update-interval', 'n_intervals')]
)
def update_data(n_intervals):
    """Update cluster data periodically"""
    success = state_manager.update_state()
    if not success:
        return [dash.no_update]
    
    current_time = datetime.now()
    return [{
        'timestamp': current_time.isoformat(),
        'update_success': success
    }]

# Dendrogram callback
@app.callback(
    dash.Output('dendrogram-graph', 'figure'),
    [
        dash.Input('update-interval', 'n_intervals'),
        dash.Input('time-slider', 'value')
    ]
)
def update_dendrogram(n_intervals, slider_value):
    """Update dendrogram visualization based on selected timestamp"""
    try:
        # Get available timestamps from history
        timestamps = sorted(state_manager.history.keys())
        if not timestamps:
            return create_placeholder_figure("No data available")
        
        # Select timestamp based on slider value
        selected_timestamp = slider_value_to_timestamp(slider_value, timestamps)
        if selected_timestamp is None:
            return create_placeholder_figure("Invalid timestamp selected")
        
        state = state_manager.history[selected_timestamp]
        
        # Create dendrogram with error handling
        try:
            linkage_matrix = state['linkage_matrix']
            labels = [mt5_to_display_format(pair) for pair in state_manager.currency_pairs]

            # Debug: Check linkage matrix values
            print(f"Linkage matrix shape: {linkage_matrix.shape}")
            print(f"Number of labels: {len(labels)}")
            print(f"Linkage matrix sample values:")
            print(f"  First row: {linkage_matrix[0] if len(linkage_matrix) > 0 else 'Empty'}")
            print(f"  Last row: {linkage_matrix[-1] if len(linkage_matrix) > 0 else 'Empty'}")
            print(f"  Max cluster index: {np.max(linkage_matrix[:, :2]) if len(linkage_matrix) > 0 else 'N/A'}")

            # Verify linkage matrix format
            # For scipy linkage format: each row should be [cluster1, cluster2, distance, count]
            # cluster indices should be < n + i where n is number of original points
            n_points = len(labels)
            max_cluster_idx = np.max(linkage_matrix[:, :2])
            expected_max = n_points + len(linkage_matrix) - 1

            print(f"Max cluster index in linkage: {max_cluster_idx}")
            print(f"Expected max index: {expected_max}")

            # Try creating dendrogram with scipy first to validate linkage matrix
            from scipy.cluster.hierarchy import dendrogram as scipy_dendrogram
            import matplotlib.pyplot as plt

            # Test with scipy (this will help us debug)
            plt.figure(figsize=(1, 1))  # Small figure just for testing
            scipy_result = scipy_dendrogram(linkage_matrix, labels=labels, no_plot=True)
            plt.close()  # Close the test figure

            print("Scipy dendrogram validation passed")

            # Now try plotly
            fig = ff.create_dendrogram(
                linkage_matrix,
                labels=labels,
                orientation='left',
                color_threshold=state_manager.distance_threshold
            )
        except Exception as e:
            print(f"Error creating dendrogram: {e}")
            import traceback
            traceback.print_exc()
            # Create a placeholder figure
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating dendrogram: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False
            )
        
        # Update layout for professional appearance
        fig.update_layout(
            title={
                'text': f"Currency Correlation Clusters at {selected_timestamp.strftime('%Y-%m-%d %H:%M')}",
                'x': 0.5,
                'xanchor': 'center'
            },
            font={'family': 'Arial, sans-serif'},
            plot_bgcolor='white',
            paper_bgcolor='white',
            height=600,
            margin={'l': 150, 'r': 50, 't': 50, 'b': 50},
            showlegend=False
        )
        
        # Add hover information
        for trace in fig.data:
            if trace.type == 'scatter':
                trace.hovertemplate = '<b>%{text}</b><br>Distance: %{y:.3f}<br>Click to select cluster<extra></extra>'
                trace.text = [
                    pair if isinstance(pair, str) else 'Cluster'
                    for pair in trace.text or []
                ]
        
        return fig
        
    except Exception as e:
        print(f"Error updating dendrogram: {str(e)}")
        return create_placeholder_figure("Error generating dendrogram")

# Dendrogram click handler callback
@app.callback(
    dash.Output('selected-cluster-store', 'data', allow_duplicate=True),
    [dash.Input('dendrogram-graph', 'clickData')],
    [dash.State('selected-cluster-store', 'data'), dash.State('time-slider', 'value')],
    prevent_initial_call=True
)
def handle_dendrogram_click(click_data, current_selection, slider_value):
    """Handle click events on dendrogram"""
    selected_cluster_data = current_selection
    
    if click_data and 'points' in click_data:
        try:
            # Extract cluster information from click
            point = click_data['points'][0]
            # For dendrogram, we need to determine which cluster was clicked
            # This is complex since dendrogram doesn't directly expose cluster IDs
            # We'll use a simplified approach based on the y-coordinate
            
            # Get current state to find cluster assignments
            timestamps = sorted(state_manager.history.keys())
            if timestamps:
                idx = min(int(slider_value * len(timestamps) / 100), len(timestamps) - 1)
                timestamp = timestamps[idx]
                state = state_manager.history[timestamp]
                
                # Find the most relevant cluster based on the clicked point
                # This is a simplified approach - in a real implementation,
                # you'd need more sophisticated cluster identification
                clusters = state['cluster_assignments']
                unique_clusters = sorted(set(clusters))
                
                # Use the first cluster as default (this could be improved)
                if unique_clusters:
                    selected_cluster_data = {'cluster_id': unique_clusters[0]}
                    
        except Exception as e:
            print(f"Error processing dendrogram click: {str(e)}")
    
    return selected_cluster_data

# Sankey diagram callback for updates
@app.callback(
    dash.Output('sankey-graph', 'figure'),
    [
        dash.Input('update-interval', 'n_intervals'),
        dash.Input('time-slider', 'value')
    ]
)
def update_sankey_figure(n_intervals, slider_value):
    """Update Sankey diagram showing cluster evolution up to selected time"""
    try:
        # Get available timestamps from history
        timestamps = sorted(state_manager.history.keys())
        if not timestamps:
            return create_placeholder_figure("No data available")
        
        # Select end time based on slider value
        selected_timestamp = slider_value_to_timestamp(slider_value, timestamps)
        if selected_timestamp is None:
            return create_placeholder_figure("Invalid timestamp selected")
        
        # Calculate time window ending at selected timestamp
        start_time = selected_timestamp - state_manager.analysis_window
        
        # Get Sankey data for the time window
        sankey_data = state_manager.get_sankey_data(start_time, selected_timestamp)
        if not sankey_data:
            return create_placeholder_figure("Insufficient history for Sankey diagram")
        
        # Create Sankey diagram
        fig = go.Figure(data=[go.Sankey(
            node=dict(
                pad=15,
                thickness=20,
                line=dict(color="black", width=0.5),
                label=[node['name'] for node in sankey_data['nodes']],
                color=[COLOR_SCHEME[i % len(COLOR_SCHEME)] for i in range(len(sankey_data['nodes']))],
                customdata=[node['members'] for node in sankey_data['nodes']],
                hovertemplate='<b>%{label}</b><br>' +
                            'Members: %{customdata}<br>' +
                            'Click to select cluster<extra></extra>'
            ),
            link=dict(
                source=[link['source'] for link in sankey_data['links']],
                target=[link['target'] for link in sankey_data['links']],
                value=[link['value'] for link in sankey_data['links']],
                hovertemplate='<b>Flow</b><br>' +
                            'From: %{source.label}<br>' +
                            'To: %{target.label}<br>' +
                            'Count: %{value}<br>' +
                            '<extra></extra>'
            )
        )])
        
        # Update layout
        fig.update_layout(
            title={
                'text': f'Cluster Evolution Timeline (up to {selected_timestamp.strftime("%Y-%m-%d %H:%M")})',
                'x': 0.5,
                'xanchor': 'center'
            },
            font={'family': 'Arial, sans-serif'},
            plot_bgcolor='white',
            paper_bgcolor='white',
            height=600,
            margin={'l': 50, 'r': 50, 't': 50, 'b': 50}
        )
        
        return fig
        
    except Exception as e:
        print(f"Error updating Sankey diagram: {str(e)}")
        return create_placeholder_figure("Error generating Sankey diagram")

# Sankey click handler callback
@app.callback(
    dash.Output('selected-cluster-store', 'data', allow_duplicate=True),
    [dash.Input('sankey-graph', 'clickData')],
    [dash.State('selected-cluster-store', 'data')],
    prevent_initial_call=True
)
def handle_sankey_click(click_data, current_selection):
    """Handle click events on Sankey diagram nodes"""
    selected_cluster_data = current_selection
    
    if click_data and 'points' in click_data:
        try:
            # Extract cluster information from Sankey node click
            point = click_data['points'][0]
            if 'label' in point:
                # Extract cluster ID from label (format: "Cluster X")
                label = point['label']
                if label.startswith('Cluster '):
                    cluster_id = int(label.split(' ')[1])
                    selected_cluster_data = {'cluster_id': cluster_id}
                    
        except Exception as e:
            print(f"Error processing Sankey click: {str(e)}")
    
    return selected_cluster_data
    """Update Sankey diagram showing cluster evolution and handle click events"""
    ctx = callback_context
    
    # Handle click events
    selected_cluster_data = current_selection
    if ctx.triggered and any('clickData' in t['prop_id'] for t in ctx.triggered):
        if click_data and 'points' in click_data:
            try:
                # Extract cluster information from Sankey node click
                point = click_data['points'][0]
                if 'label' in point:
                    # Extract cluster ID from label (format: "Cluster X")
                    label = point['label']
                    if label.startswith('Cluster '):
                        cluster_id = int(label.split(' ')[1])
                        selected_cluster_data = {'cluster_id': cluster_id}
                        
            except Exception as e:
                print(f"Error processing Sankey click: {str(e)}")
    
    try:
        current_time = datetime.now()
        start_time = current_time - state_manager.analysis_window
        
        # Get Sankey data
        sankey_data = state_manager.get_sankey_data(start_time, current_time)
        if not sankey_data:
            return create_placeholder_figure("Insufficient history for Sankey diagram"), selected_cluster_data
        
        # Create Sankey diagram
        fig = go.Figure(data=[go.Sankey(
            node=dict(
                pad=15,
                thickness=20,
                line=dict(color="black", width=0.5),
                label=[node['name'] for node in sankey_data['nodes']],
                color=[COLOR_SCHEME[i % len(COLOR_SCHEME)] for i in range(len(sankey_data['nodes']))],
                customdata=[node['members'] for node in sankey_data['nodes']],
                hovertemplate='<b>%{label}</b><br>' +
                            'Members: %{customdata}<br>' +
                            'Click to select cluster<extra></extra>'
            ),
            link=dict(
                source=[link['source'] for link in sankey_data['links']],
                target=[link['target'] for link in sankey_data['links']],
                value=[link['value'] for link in sankey_data['links']],
                hovertemplate='<b>Flow</b><br>' +
                            'From: %{source.label}<br>' +
                            'To: %{target.label}<br>' +
                            'Count: %{value}<br>' +
                            '<extra></extra>'
            )
        )])
        
        # Update layout
        fig.update_layout(
            title={
                'text': 'Cluster Evolution Timeline',
                'x': 0.5,
                'xanchor': 'center'
            },
            font={'family': 'Arial, sans-serif'},
            plot_bgcolor='white',
            paper_bgcolor='white',
            height=600,
            margin={'l': 50, 'r': 50, 't': 50, 'b': 50}
        )
        
        return fig, selected_cluster_data
        
    except Exception as e:
        print(f"Error updating Sankey diagram: {str(e)}")
        return create_placeholder_figure("Error generating Sankey diagram"), selected_cluster_data
# Statistics panel callback
@app.callback(
    dash.Output('stats-content', 'children'),
    [
        dash.Input('update-interval', 'n_intervals'),
        dash.Input('time-slider', 'value')
    ]
)
def update_stats_panel(n_intervals, slider_value):
    """Update statistics panel with cluster metrics for selected timestamp"""
    try:
        # Get available timestamps from history
        timestamps = sorted(state_manager.history.keys())
        if not timestamps:
            return html.Div([
                html.P("No cluster statistics available.", className="text-muted text-center mb-0")
            ])
        
        # Select timestamp based on slider value
        selected_timestamp = slider_value_to_timestamp(slider_value, timestamps)
        if selected_timestamp is None:
            return html.Div([
                html.P("Invalid timestamp selected.", className="text-danger text-center mb-0")
            ])
        
        # Get cluster statistics for selected timestamp
        stats = state_manager.get_cluster_statistics(selected_timestamp)
        if not stats:
            return html.Div([
                html.P("No cluster statistics available for selected time.", className="text-muted text-center mb-0")
            ])
        
        # Create statistics display
        stats_cards = []
        
        # Overall metrics card
        overall_card = dbc.Card([
            dbc.CardHeader([
                html.H5("Overall Metrics", className="mb-0"),
                html.Small(f"At {selected_timestamp.strftime('%Y-%m-%d %H:%M')}", className="text-muted")
            ]),
            dbc.CardBody([
                html.P(f"Number of Clusters: {stats['n_clusters']}", className="mb-1"),
                html.P(f"Silhouette Score: {stats['silhouette_score']:.3f}", className="mb-1"),
                html.P(f"Cophenetic Correlation: {stats['cophenetic_correlation']:.3f}", className="mb-0")
            ])
        ], className="mb-3")
        stats_cards.append(overall_card)
        
        # Individual cluster cards
        for cluster_detail in stats['cluster_details']:
            cluster_card = dbc.Card([
                dbc.CardHeader([
                    html.H6(f"Cluster {cluster_detail['cluster_id']}", className="mb-0")
                ]),
                dbc.CardBody([
                    html.P(f"Size: {cluster_detail['size']}", className="mb-1 small"),
                    html.P(f"Members: {', '.join(cluster_detail['members'])}", className="mb-1 small"),
                    html.P(f"Avg Intra-Correlation: {cluster_detail['avg_intra_correlation']:.3f}", className="mb-1 small"),
                    html.P(f"Avg Volatility: {cluster_detail['avg_volatility']:.3f}", className="mb-1 small"),
                    html.P(f"Cohesion Score: {cluster_detail['cohesion_score']:.3f}", className="mb-0 small")
                ])
            ], className="mb-2")
            stats_cards.append(cluster_card)
        
        return html.Div(stats_cards)
        
    except Exception as e:
        print(f"Error updating statistics panel: {str(e)}")
        return html.Div([
            html.P("Error loading cluster statistics.", className="text-danger text-center mb-0")
        ])


# Event Log callback
@app.callback(
    dash.Output('events-content', 'children'),
    [dash.Input('update-interval', 'n_intervals')]
)
def update_event_log(n_intervals):
    """Update event log with interactive table of cluster events"""
    try:
        # Get event log from state manager
        events = state_manager.event_log
        
        if not events:
            return html.Div([
                html.P("No cluster events detected yet.", className="text-muted text-center mb-0")
            ])
        
        # Sort events by timestamp (newest first)
        sorted_events = sorted(events, key=lambda x: x['timestamp'], reverse=True)
        
        # Create table headers
        table_header = html.Thead([
            html.Tr([
                html.Th("Timestamp", className="text-center"),
                html.Th("Type", className="text-center"),
                html.Th("Description", className="text-center"),
                html.Th("ARI Score", className="text-center"),
                html.Th("Actions", className="text-center")
            ])
        ])
        
        # Create table rows
        table_rows = []
        for i, event in enumerate(sorted_events):
            # Determine event type display
            event_type = event.get('type', 'cluster_change')
            event_badge_color = "warning" if event_type == "cluster_change" else "info"
            
            # Format timestamp
            timestamp_str = event['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            
            # Create table row
            row = html.Tr([
                html.Td(timestamp_str, className="text-center"),
                html.Td([
                    dbc.Badge(
                        event_type.replace('_', ' ').title(),
                        color=event_badge_color,
                        className="me-1"
                    )
                ], className="text-center"),
                html.Td(event.get('description', 'N/A'), className="text-center"),
                html.Td(f"{event.get('ari_score', 0):.3f}", className="text-center"),
                html.Td([
                    dbc.Button(
                        "Details",
                        id={"type": "event-detail-btn", "index": i},
                        color="primary",
                        size="sm",
                        outline=True,
                        className="me-1"
                    )
                ], className="text-center")
            ], className="align-middle")
            
            table_rows.append(row)
        
        # Create table body
        table_body = html.Tbody(table_rows)
        
        # Create complete table
        table = dbc.Table([table_header, table_body],
                         striped=True,
                         bordered=True,
                         hover=True,
                         responsive=True,
                         className="mb-0")
        
        return html.Div([
            html.Div([
                html.H6(f"Recent Events ({len(sorted_events)} total)", className="mb-3")
            ]),
            table
        ])
        
    except Exception as e:
        print(f"Error updating event log: {str(e)}")
        return html.Div([
            html.P("Error loading event log.", className="text-danger text-center mb-0")
        ])

# Modal callback for event details
@app.callback(
    [
        dash.Output('event-modal', 'is_open'),
        dash.Output('modal-content', 'children'),
        dash.Output('modal-title', 'children')
    ],
    [
        dash.Input({'type': 'event-detail-btn', 'index': dash.dependencies.ALL}, 'n_clicks'),
        dash.Input('modal-close-button', 'n_clicks')
    ],
    [dash.State('event-modal', 'is_open')]
)
def toggle_event_modal(detail_clicks, close_clicks, is_open):
    """Handle event detail modal opening and closing"""
    ctx = callback_context
    
    if not ctx.triggered:
        return False, html.Div(), "Event Details"
    
    # Get the trigger source
    trigger_id = ctx.triggered[0]['prop_id']
    
    # Handle close button
    if 'modal-close-button' in trigger_id:
        return False, html.Div(), "Event Details"
    
    # Handle detail button clicks
    if 'event-detail-btn' in trigger_id and any(detail_clicks):
        try:
            # Find which button was clicked
            clicked_index = None
            for i, clicks in enumerate(detail_clicks):
                if clicks and clicks > 0:
                    clicked_index = i
                    break
            
            if clicked_index is None:
                return False, html.Div(), "Event Details"
            
            # Get the event data
            events = state_manager.event_log
            if not events or clicked_index >= len(events):
                return False, html.Div(), "Event Details"
            
            # Sort events by timestamp (newest first) to match the table
            sorted_events = sorted(events, key=lambda x: x['timestamp'], reverse=True)
            event = sorted_events[clicked_index]
            
            # Create modal content
            modal_content = create_event_comparison_content(event)
            
            # Create modal title
            timestamp_str = event['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            modal_title = f"Event Details - {timestamp_str}"
            
            return True, modal_content, modal_title
            
        except Exception as e:
            print(f"Error opening event modal: {str(e)}")
            error_content = html.Div([
                dbc.Alert(
                    "Error loading event details. Please try again.",
                    color="danger",
                    className="mb-0"
                )
            ])
            return True, error_content, "Error"
    
    return is_open, html.Div(), "Event Details"

def create_event_comparison_content(event):
    """Create the before/after comparison content for the modal"""
    try:
        # Get event timestamp and find previous state
        event_time = event['timestamp']
        
        # Get all timestamps and find the ones before and at event time
        timestamps = sorted(state_manager.history.keys())
        
        # Find the states before and after the event
        before_state = None
        after_state = None
        
        for i, ts in enumerate(timestamps):
            if ts == event_time:
                after_state = state_manager.history[ts]
                if i > 0:
                    before_state = state_manager.history[timestamps[i-1]]
                break
        
        if not before_state or not after_state:
            return html.Div([
                dbc.Alert(
                    "Historical data not available for comparison.",
                    color="warning",
                    className="mb-0"
                )
            ])
        
        # Create comparison content
        content = html.Div([
            # Event summary
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Event Summary", className="mb-0")
                ]),
                dbc.CardBody([
                    html.Div([
                        html.Strong("Type: "), event.get('type', 'cluster_change').replace('_', ' ').title()
                    ], className="mb-2"),
                    html.Div([
                        html.Strong("Description: "), event.get('description', 'N/A')
                    ], className="mb-2"),
                    html.Div([
                        html.Strong("Adjusted Rand Index: "), f"{event.get('ari_score', 0):.3f}"
                    ], className="mb-2"),
                    html.Div([
                        html.Strong("Affected Pairs: "),
                        ", ".join(event.get('affected_pairs', []))
                    ], className="mb-0")
                ])
            ], className="mb-4"),
            
            # Before and After comparison
            dbc.Row([
                # Before state
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H5("Before", className="mb-0 text-info")
                        ]),
                        dbc.CardBody([
                            create_cluster_state_summary(before_state, "before")
                        ])
                    ])
                ], width=6),
                
                # After state
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader([
                            html.H5("After", className="mb-0 text-success")
                        ]),
                        dbc.CardBody([
                            create_cluster_state_summary(after_state, "after")
                        ])
                    ])
                ], width=6)
            ], className="mb-4"),
            
            # Detailed changes
            dbc.Card([
                dbc.CardHeader([
                    html.H5("Detailed Changes", className="mb-0")
                ]),
                dbc.CardBody([
                    create_detailed_changes_table(event, before_state, after_state)
                ])
            ])
        ])
        
        return content
        
    except Exception as e:
        print(f"Error creating event comparison content: {str(e)}")
        return html.Div([
            dbc.Alert(
                "Error generating comparison content.",
                color="danger",
                className="mb-0"
            )
        ])

def create_cluster_state_summary(state, prefix):
    """Create a summary of cluster state"""
    try:
        clusters = state['cluster_assignments']
        n_clusters = len(set(clusters))
        
        # Group pairs by cluster
        cluster_groups = {}
        for i, cluster_id in enumerate(clusters):
            if cluster_id not in cluster_groups:
                cluster_groups[cluster_id] = []
            cluster_groups[cluster_id].append(mt5_to_display_format(state_manager.currency_pairs[i]))
        
        # Create cluster summary
        cluster_items = []
        for cluster_id, pairs in sorted(cluster_groups.items()):
            cluster_items.append(
                html.Div([
                    html.Strong(f"Cluster {cluster_id}: "),
                    ", ".join(pairs)
                ], className="mb-2")
            )
        
        return html.Div([
            html.Div([
                html.Strong("Total Clusters: "), str(n_clusters)
            ], className="mb-3"),
            html.Div([
                html.Strong("Cluster Composition:")
            ], className="mb-2"),
            html.Div(cluster_items)
        ])
        
    except Exception as e:
        print(f"Error creating cluster state summary: {str(e)}")
        return html.P("Error loading cluster state summary.")

def create_detailed_changes_table(event, before_state, after_state):
    """Create a detailed table of changes between states"""
    try:
        prev_clusters = event.get('prev_clusters', [])
        curr_clusters = event.get('curr_clusters', [])
        
        if not prev_clusters or not curr_clusters:
            return html.P("Detailed change data not available.")
        
        # Create table of changes
        table_header = html.Thead([
            html.Tr([
                html.Th("Currency Pair", className="text-center"),
                html.Th("Before Cluster", className="text-center"),
                html.Th("After Cluster", className="text-center"),
                html.Th("Change", className="text-center")
            ])
        ])
        
        table_rows = []
        for i, (prev_cluster, curr_cluster) in enumerate(zip(prev_clusters, curr_clusters)):
            pair = mt5_to_display_format(state_manager.currency_pairs[i])
            
            # Determine if there was a change
            changed = prev_cluster != curr_cluster
            change_badge = dbc.Badge(
                "Changed" if changed else "No Change",
                color="warning" if changed else "success",
                className="me-1"
            )
            
            row = html.Tr([
                html.Td(pair, className="text-center"),
                html.Td(str(prev_cluster), className="text-center"),
                html.Td(str(curr_cluster), className="text-center"),
                html.Td(change_badge, className="text-center")
            ], className="align-middle")
            
            table_rows.append(row)
        
        table_body = html.Tbody(table_rows)
        
        return dbc.Table([table_header, table_body],
                        striped=True,
                        bordered=True,
                        hover=True,
                        responsive=True,
                        className="mb-0")
        
    except Exception as e:
        print(f"Error creating detailed changes table: {str(e)}")
        return html.P("Error loading detailed changes.")

# Run the server
if __name__ == '__main__':
    app.run(debug=True, port=8050)

# Regime Configuration Callback
@app.callback(
    dash.Output('regime-config-store', 'data'),
    [
        dash.Input('n-clusters-slider', 'value'),
        dash.Input('timezone-dropdown', 'value'),
        dash.Input('analysis-period-dropdown', 'value')
    ]
)
def update_regime_config(n_clusters, timezone_offset, analysis_period):
    """Update regime configuration store"""
    return {
        'n_clusters': n_clusters,
        'timezone_offset': timezone_offset,
        'analysis_period': analysis_period
    }

# Regime Update Callback
@app.callback(
    dash.Output('regime-calendar-content', 'children'),
    [
        dash.Input('regime-update-interval', 'n_intervals'),
        dash.Input('update-regimes-button', 'n_clicks')
    ],
    [
        dash.State('regime-config-store', 'data')
    ]
)
def update_regime_calendar(n_intervals, button_clicks, regime_config):
    """Update regime calendar visualization"""
    try:
        # Update volatility regimes with current configuration
        success = state_manager.update_volatility_regimes(
            num_days_history=regime_config.get('analysis_period', 30),
            n_clusters=regime_config.get('n_clusters', 4),
            timezone_offset=regime_config.get('timezone_offset', 0)
        )
        
        if not success:
            return html.Div([
                dbc.Alert(
                    [
                        html.I(className="fas fa-exclamation-triangle me-2"),
                        "Failed to update volatility regimes. Please check data availability."
                    ],
                    color="warning",
                    className="mb-3"
                ),
                html.P("Possible issues:", className="small fw-bold"),
                html.Ul([
                    html.Li("Insufficient historical data available", className="small"),
                    html.Li("Data quality below minimum threshold", className="small"),
                    html.Li("Clustering algorithm failed to converge", className="small")
                ], className="small text-muted")
            ])
        
        # Get regime calendar data
        calendar_data = state_manager.get_regime_calendar_data()
        
        if calendar_data is None:
            return html.Div([
                dbc.Alert(
                    [
                        html.I(className="fas fa-info-circle me-2"),
                        "No regime calendar data available yet. Please wait for analysis to complete."
                    ],
                    color="info"
                )
            ])
        
        return create_regime_calendar_visualization(calendar_data)
        
    except Exception as e:
        return html.Div([
            dbc.Alert(
                [
                    html.I(className="fas fa-exclamation-triangle me-2"),
                    f"Error updating regime calendar: {str(e)}"
                ],
                color="danger"
            )
        ])

# Regime Legend Callback
@app.callback(
    dash.Output('regime-legend-content', 'children'),
    [
        dash.Input('regime-update-interval', 'n_intervals'),
        dash.Input('update-regimes-button', 'n_clicks')
    ]
)
def update_regime_legend(n_intervals, button_clicks):
    """Update regime legend display"""
    try:
        calendar_data = state_manager.get_regime_calendar_data()
        
        if calendar_data is None:
            return html.Div([
                html.P("No regime data available", className="text-muted small")
            ])
        
        return create_regime_legend(calendar_data)
        
    except Exception as e:
        return html.Div([
            dbc.Alert(f"Error loading regime legend: {str(e)}", color="danger", className="small")
        ])

# Regime Statistics Callback
@app.callback(
    dash.Output('regime-stats-content', 'children'),
    [
        dash.Input('regime-update-interval', 'n_intervals'),
        dash.Input('update-regimes-button', 'n_clicks')
    ]
)
def update_regime_statistics(n_intervals, button_clicks):
    """Update regime summary statistics"""
    try:
        calendar_data = state_manager.get_regime_calendar_data()
        
        if calendar_data is None:
            return html.Div([
                html.P("No statistics available", className="text-muted small")
            ])
        
        return create_regime_statistics_panel(calendar_data)
        
    except Exception as e:
        return html.Div([
            dbc.Alert(f"Error loading statistics: {str(e)}", color="danger", className="small")
        ])

def create_regime_calendar_visualization(calendar_data):
    """Create the regime calendar visualization"""
    try:
        # Group calendar data by date
        date_regimes = {}
        for entry in calendar_data['calendar_data']:
            date = entry['date']
            if date not in date_regimes:
                date_regimes[date] = []
            date_regimes[date].append(entry)
        
        # Create calendar grid
        calendar_rows = []
        dates = sorted(date_regimes.keys())
        
        if not dates:
            return html.Div([
                dbc.Alert("No calendar data available", color="info")
            ])
        
        # Group dates by week
        import datetime as dt
        from datetime import datetime
        
        weeks = []
        current_week = []
        
        for date_str in dates:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            
            # Start new week on Monday
            if current_week and date_obj.weekday() == 0:
                weeks.append(current_week)
                current_week = []
            
            current_week.append((date_str, date_obj))
        
        if current_week:
            weeks.append(current_week)
        
        # Create calendar grid
        calendar_grid = []
        
        # Header row
        header_row = dbc.Row([
            dbc.Col(html.Div("Mon", className="text-center fw-bold small"), width=1),
            dbc.Col(html.Div("Tue", className="text-center fw-bold small"), width=1),
            dbc.Col(html.Div("Wed", className="text-center fw-bold small"), width=1),
            dbc.Col(html.Div("Thu", className="text-center fw-bold small"), width=1),
            dbc.Col(html.Div("Fri", className="text-center fw-bold small"), width=1),
            dbc.Col(html.Div("Sat", className="text-center fw-bold small"), width=1),
            dbc.Col(html.Div("Sun", className="text-center fw-bold small"), width=1)
        ])
        calendar_grid.append(header_row)
        
        # Calendar weeks
        for week in weeks:
            week_cells = [None] * 7  # Monday to Sunday
            
            for date_str, date_obj in week:
                weekday = date_obj.weekday()  # Monday = 0
                
                # Calculate dominant regime for this date
                day_entries = date_regimes.get(date_str, [])
                if day_entries:
                    # Find most common regime
                    regime_counts = {}
                    for entry in day_entries:
                        regime_id = entry.get('regime_id', 0)
                        regime_counts[regime_id] = regime_counts.get(regime_id, 0) + 1
                    
                    dominant_regime = max(regime_counts, key=regime_counts.get)
                    regime_color = REGIME_COLORS.get(dominant_regime, '#cccccc')
                    
                    # Get regime name
                    regime_name = next((e['regime_name'] for e in day_entries if e.get('regime_id') == dominant_regime), 'Unknown')
                    
                    # Create cell
                    cell = dbc.Col([
                        html.Div([
                            html.Div(str(date_obj.day), className="fw-bold"),
                            html.Div(regime_name[:8], className="small text-truncate")
                        ], 
                        id={"type": "calendar-cell", "date": date_str},
                        className="regime-calendar-cell p-2 text-center border rounded",
                        style={
                            'backgroundColor': regime_color,
                            'color': 'white' if dominant_regime in [0, 2, 3, 4] else 'black',
                            'minHeight': '60px',
                            'cursor': 'pointer'
                        },
                        title=f"{date_str}: {regime_name} - Click for detailed analysis",
                        n_clicks=0
                        )
                    ], width=1)
                    
                    week_cells[weekday] = cell
            
            # Fill empty cells
            for i in range(7):
                if week_cells[i] is None:
                    week_cells[i] = dbc.Col(html.Div("", className="regime-calendar-cell p-2", style={'minHeight': '60px'}), width=1)
            
            calendar_grid.append(dbc.Row(week_cells, className="mb-1"))
        
        return html.Div([
            html.Div([
                html.H6(f"Regime Calendar - {calendar_data['date_range'][0]} to {calendar_data['date_range'][1]}", className="text-center mb-3"),
                html.Div(calendar_grid)
            ])
        ])
        
    except Exception as e:
        return html.Div([
            dbc.Alert(f"Error creating calendar visualization: {str(e)}", color="danger")
        ])

def create_regime_legend(calendar_data):
    """Create the regime legend display"""
    try:
        # Get unique regimes from calendar data
        regimes = {}
        for entry in calendar_data['calendar_data']:
            regime_id = entry.get('regime_id', 0)
            if regime_id not in regimes:
                regimes[regime_id] = {
                    'name': entry.get('regime_name', 'Unknown'),
                    'count': 0
                }
            regimes[regime_id]['count'] += 1
        
        # Create legend items
        legend_items = []
        for regime_id, regime_info in sorted(regimes.items()):
            color = REGIME_COLORS.get(regime_id, '#cccccc')
            
            legend_item = dbc.Row([
                dbc.Col([
                    html.Div(
                        "",
                        className="regime-legend-color",
                        style={
                            'backgroundColor': color,
                            'width': '20px',
                            'height': '20px',
                            'display': 'inline-block',
                            'borderRadius': '3px',
                            'marginRight': '8px'
                        }
                    )
                ], width=2),
                dbc.Col([
                    html.Div([
                        html.Div(regime_info['name'], className="fw-bold small"),
                        html.Div(f"{regime_info['count']} occurrences", className="text-muted small")
                    ])
                ], width=10)
            ], className="mb-2")
            
            legend_items.append(legend_item)
        
        return html.Div(legend_items)
        
    except Exception as e:
        return html.Div([
            dbc.Alert(f"Error creating legend: {str(e)}", color="danger", className="small")
        ])

def create_regime_statistics_panel(calendar_data):
    """Create the regime statistics panel"""
    try:
        clustering_quality = calendar_data.get('clustering_quality', {})
        
        # Calculate regime distribution
        regime_counts = {}
        total_observations = len(calendar_data['calendar_data'])
        
        for entry in calendar_data['calendar_data']:
            regime_id = entry.get('regime_id', 0)
            regime_counts[regime_id] = regime_counts.get(regime_id, 0) + 1
        
        # Create statistics display
        stats_content = []
        
        # Quality metrics
        quality_metrics = dbc.Card([
            dbc.CardHeader([
                html.H6("Clustering Quality", className="m-0")
            ]),
            dbc.CardBody([
                html.Div([
                    html.Div([
                        html.Strong("Silhouette Score: "),
                        html.Span(f"{clustering_quality.get('silhouette_score', 0):.3f}")
                    ], className="mb-2 small"),
                    html.Div([
                        html.Strong("Calinski-Harabasz: "),
                        html.Span(f"{clustering_quality.get('calinski_harabasz_score', 0):.1f}")
                    ], className="mb-2 small"),
                    html.Div([
                        html.Strong("Converged: "),
                        html.Span("Yes" if clustering_quality.get('converged', False) else "No")
                    ], className="small")
                ])
            ])
        ], className="mb-3")
        
        stats_content.append(quality_metrics)
        
        # Regime distribution
        distribution_items = []
        for regime_id, count in sorted(regime_counts.items()):
            percentage = (count / total_observations) * 100
            
            regime_name = next((entry['regime_name'] for entry in calendar_data['calendar_data'] 
                              if entry.get('regime_id') == regime_id), 'Unknown')
            
            distribution_items.append(
                html.Div([
                    html.Div([
                        html.Span(regime_name, className="fw-bold"),
                        html.Span(f" ({percentage:.1f}%)", className="text-muted")
                    ], className="small")
                ], className="mb-1")
            )
        
        distribution_card = dbc.Card([
            dbc.CardHeader([
                html.H6("Regime Distribution", className="m-0")
            ]),
            dbc.CardBody(distribution_items)
        ], className="mb-3")
        
        stats_content.append(distribution_card)
        
        # Data summary
        data_summary = dbc.Card([
            dbc.CardHeader([
                html.H6("Data Summary", className="m-0")
            ]),
            dbc.CardBody([
                html.Div([
                    html.Div([
                        html.Strong("Total Observations: "),
                        html.Span(str(total_observations))
                    ], className="mb-2 small"),
                    html.Div([
                        html.Strong("Regimes Identified: "),
                        html.Span(str(calendar_data.get('n_regimes', 0)))
                    ], className="mb-2 small"),
                    html.Div([
                        html.Strong("Symbols Analyzed: "),
                        html.Span(str(len(calendar_data.get('symbols', []))))
                    ], className="mb-2 small"),
                    html.Div([
                        html.Strong("Last Updated: "),
                        html.Span(calendar_data.get('last_updated', 'Unknown'))
                    ], className="small")
                ])
            ])
        ])
        
        stats_content.append(data_summary)
        
        return html.Div(stats_content)
        
    except Exception as e:
        return html.Div([
            dbc.Alert(f"Error creating statistics: {str(e)}", color="danger", className="small")
        ])

# Daily Drill-Down Modal Callbacks
@app.callback(
    [
        Output('daily-drill-modal', 'is_open'),
        Output('selected-date-store', 'data'),
        Output('daily-modal-title', 'children')
    ],
    [
        Input({'type': 'calendar-cell', 'date': dependencies.ALL}, 'n_clicks'),
        Input('daily-modal-close-button', 'n_clicks')
    ],
    [
        State('daily-drill-modal', 'is_open'),
        State('selected-date-store', 'data')
    ]
)
def toggle_daily_modal(calendar_clicks, close_clicks, is_open, selected_date):
    """Handle daily drill-down modal opening and closing"""
    ctx = callback_context
    
    if not ctx.triggered:
        return False, selected_date, "Daily Volatility Analysis"
    
    trigger_id = ctx.triggered[0]['prop_id']
    
    # Handle close button
    if 'daily-modal-close-button' in trigger_id:
        return False, selected_date, "Daily Volatility Analysis"
    
    # Handle calendar cell clicks
    if 'calendar-cell' in trigger_id and any(calendar_clicks):
        try:
            # Find which calendar cell was clicked
            clicked_date = None
            for i, clicks in enumerate(calendar_clicks):
                if clicks and clicks > 0:
                    # Extract date from the trigger ID
                    import json
                    trigger_data = json.loads(trigger_id.split('.')[0])
                    clicked_date = trigger_data['date']
                    break
            
            if clicked_date:
                modal_title = f"Daily Volatility Analysis - {clicked_date}"
                return True, clicked_date, modal_title
            
        except Exception as e:
            print(f"Error handling calendar click: {str(e)}")
    
    return is_open, selected_date, "Daily Volatility Analysis"

@app.callback(
    [
        Output('daily-modal-content', 'children'),
        Output('daily-modal-footer', 'children')
    ],
    [
        Input('selected-date-store', 'data'),
        Input('daily-drill-modal', 'is_open')
    ]
)
def update_daily_modal_content(selected_date, is_open):
    """Update daily drill-down modal content with detailed volatility analysis"""
    if not is_open or not selected_date:
        return html.Div(), html.Div()
    
    try:
        # Get intraday regime match data
        regime_match_data = state_manager.get_intraday_regime_match(selected_date)
        
        if regime_match_data is None:
            return html.Div([
                dbc.Alert(
                    f"No volatility data available for {selected_date}. Please ensure volatility regimes have been calculated.",
                    color="warning"
                )
            ]), html.Div()
        
        # Generate mock 24-hour volatility vector for demonstration
        # In production, this would come from the actual data
        import random
        random.seed(hash(selected_date))  # Consistent data for same date
        
        volatility_vector = []
        for hour in range(24):
            if 0 <= hour <= 6:  # Asian session
                base_vol = 0.03 + random.uniform(-0.01, 0.01)
            elif 8 <= hour <= 16:  # European/US overlap
                base_vol = 0.05 + random.uniform(-0.02, 0.02)
            elif 13 <= hour <= 21:  # US session
                base_vol = 0.06 + random.uniform(-0.015, 0.015)
            else:  # Transition periods
                base_vol = 0.04 + random.uniform(-0.015, 0.015)
            
            volatility_vector.append(max(0.01, base_vol))
        
        # Create detailed analysis charts
        modal_content = create_daily_analysis_content(selected_date, regime_match_data, volatility_vector)
        
        # Create summary statistics for footer
        footer_content = create_daily_analysis_footer(selected_date, regime_match_data, volatility_vector)
        
        return modal_content, footer_content
        
    except Exception as e:
        error_content = html.Div([
            dbc.Alert(
                f"Error loading daily analysis for {selected_date}: {str(e)}",
                color="danger"
            )
        ])
        return error_content, html.Div()

def create_daily_analysis_content(selected_date, regime_match_data, volatility_vector):
    """Create the detailed daily volatility analysis content"""
    try:
        # Extract regime information
        hourly_matches = regime_match_data.get('hourly_matches', [])
        available_regimes = regime_match_data.get('available_regimes', [])
        
        # Create regime color mapping
        regime_colors = {}
        for regime in available_regimes:
            regime_id = regime.get('regime_id', 0)
            regime_colors[regime_id] = REGIME_COLORS.get(regime_id, '#cccccc')
        
        # 1. Intraday Volatility Chart with regime color-coding
        hours = list(range(24))
        regime_ids = [match.get('regime_id', 0) for match in hourly_matches]
        
        # Create color list for each hour based on regime
        colors = [regime_colors.get(regime_id, '#cccccc') for regime_id in regime_ids]
        
        intraday_fig = go.Figure()
        
        # Add volatility line
        intraday_fig.add_trace(go.Scatter(
            x=hours,
            y=volatility_vector,
            mode='lines+markers',
            name='Hourly Volatility',
            line=dict(color='#2E86AB', width=3),
            marker=dict(
                size=8,
                color=colors,
                line=dict(width=2, color='white')
            ),
            hovertemplate='<b>Hour:</b> %{x}:00<br><b>Volatility:</b> %{y:.4f}<br><extra></extra>'
        ))
        
        # Add trading session markers
        session_colors = {'Asian': '#2E8B57', 'European': '#4169E1', 'US': '#DC143C'}
        sessions = [
            {'name': 'Asian', 'start': 0, 'end': 6, 'color': session_colors['Asian']},
            {'name': 'European', 'start': 8, 'end': 16, 'color': session_colors['European']},
            {'name': 'US', 'start': 13, 'end': 21, 'color': session_colors['US']}
        ]
        
        for session in sessions:
            intraday_fig.add_vrect(
                x0=session['start'], x1=session['end'],
                fillcolor=session['color'], opacity=0.1,
                layer="below", line_width=0,
                annotation_text=session['name'],
                annotation_position="top left"
            )
        
        intraday_fig.update_layout(
            title=f"24-Hour Volatility Profile - {selected_date}",
            xaxis_title="Hour of Day",
            yaxis_title="Volatility",
            xaxis=dict(tickmode='linear', tick0=0, dtick=2),
            height=400,
            showlegend=True
        )
        
        intraday_chart = dcc.Graph(figure=intraday_fig)
        
        # 2. Regime Timeline
        timeline_fig = go.Figure()
        
        for i, match in enumerate(hourly_matches):
            regime_id = match.get('regime_id', 0)
            regime_name = match.get('regime_name', 'Unknown')
            confidence = match.get('confidence', 0)
            
            timeline_fig.add_trace(go.Bar(
                x=[i],
                y=[1],
                name=regime_name,
                marker_color=regime_colors.get(regime_id, '#cccccc'),
                hovertemplate=f'<b>Hour:</b> {i}:00<br><b>Regime:</b> {regime_name}<br><b>Confidence:</b> {confidence:.2f}<br><extra></extra>',
                showlegend=False
            ))
        
        timeline_fig.update_layout(
            title="Hourly Regime Timeline",
            xaxis_title="Hour of Day",
            yaxis=dict(showticklabels=False, title=""),
            height=150,
            margin=dict(l=20, r=20, t=40, b=40)
        )
        
        regime_timeline = dcc.Graph(figure=timeline_fig)
        
        # 3. Volatility Heatmap
        heatmap_data = np.array(volatility_vector).reshape(1, -1)
        
        heatmap_fig = go.Figure(data=go.Heatmap(
            z=heatmap_data,
            x=hours,
            y=['Volatility'],
            colorscale='Blues',
            hovertemplate='<b>Hour:</b> %{x}:00<br><b>Volatility:</b> %{z:.4f}<br><extra></extra>'
        ))
        
        heatmap_fig.update_layout(
            title="Volatility Intensity Heatmap",
            xaxis_title="Hour of Day",
            height=150
        )
        
        volatility_heatmap = dcc.Graph(figure=heatmap_fig)
        
        # 4. Comparison Chart against regime archetypes
        comparison_fig = go.Figure()
        
        # Add actual day volatility
        comparison_fig.add_trace(go.Scatter(
            x=hours,
            y=volatility_vector,
            mode='lines+markers',
            name='Selected Day',
            line=dict(color='#2E86AB', width=3)
        ))
        
        # Add regime archetypes for comparison
        for regime in available_regimes[:3]:  # Show top 3 regimes
            regime_id = regime.get('regime_id', 0)
            regime_name = regime.get('regime_name', 'Unknown')
            
            # Generate archetype pattern (mock data)
            archetype_pattern = []
            for hour in range(24):
                if regime_id == 0:  # Asian
                    base_vol = 0.04 if 0 <= hour <= 6 else 0.02
                elif regime_id == 1:  # European
                    base_vol = 0.05 if 8 <= hour <= 16 else 0.03
                elif regime_id == 2:  # US
                    base_vol = 0.06 if 13 <= hour <= 21 else 0.03
                else:
                    base_vol = 0.04
                
                archetype_pattern.append(base_vol)
            
            comparison_fig.add_trace(go.Scatter(
                x=hours,
                y=archetype_pattern,
                mode='lines',
                name=f'{regime_name} Archetype',
                line=dict(dash='dash', color=regime_colors.get(regime_id, '#cccccc'))
            ))
        
        comparison_fig.update_layout(
            title="Volatility Comparison vs Regime Archetypes",
            xaxis_title="Hour of Day",
            yaxis_title="Volatility",
            height=400
        )
        
        comparison_chart = dcc.Graph(figure=comparison_fig)
        
        # Assemble all components
        content = html.Div([
            dbc.Row([
                dbc.Col([intraday_chart], width=12)
            ], className="mb-4"),
            
            dbc.Row([
                dbc.Col([regime_timeline], width=12)
            ], className="mb-4"),
            
            dbc.Row([
                dbc.Col([volatility_heatmap], width=12)
            ], className="mb-4"),
            
            dbc.Row([
                dbc.Col([comparison_chart], width=12)
            ])
        ])
        
        return content
        
    except Exception as e:
        return html.Div([
            dbc.Alert(f"Error creating daily analysis content: {str(e)}", color="danger")
        ])

def create_daily_analysis_footer(selected_date, regime_match_data, volatility_vector):
    """Create summary statistics for the modal footer"""
    try:
        # Calculate summary statistics
        total_volatility = sum(volatility_vector)
        mean_volatility = total_volatility / len(volatility_vector)
        peak_hour = volatility_vector.index(max(volatility_vector))
        peak_value = max(volatility_vector)
        quiet_hour = volatility_vector.index(min(volatility_vector))
        quiet_value = min(volatility_vector)
        
        # Calculate regime distribution
        hourly_matches = regime_match_data.get('hourly_matches', [])
        regime_distribution = {}
        total_confidence = 0
        
        for match in hourly_matches:
            regime_name = match.get('regime_name', 'Unknown')
            confidence = match.get('confidence', 0)
            regime_distribution[regime_name] = regime_distribution.get(regime_name, 0) + 1
            total_confidence += confidence
        
        avg_confidence = total_confidence / len(hourly_matches) if hourly_matches else 0
        
        # Convert counts to percentages
        regime_distribution_pct = {
            regime: f"{(count/24)*100:.1f}%"
            for regime, count in regime_distribution.items()
        }
        
        # Create footer content
        footer_content = dbc.Row([
            dbc.Col([
                html.H6("Daily Summary", className="mb-2"),
                html.P([
                    html.Strong("Total Daily Volatility: "), f"{total_volatility:.4f}"
                ], className="mb-1 small"),
                html.P([
                    html.Strong("Mean Volatility: "), f"{mean_volatility:.4f}"
                ], className="mb-1 small"),
                html.P([
                    html.Strong("Peak: "), f"{peak_value:.4f} at {peak_hour:02d}:00"
                ], className="mb-1 small"),
                html.P([
                    html.Strong("Quiet: "), f"{quiet_value:.4f} at {quiet_hour:02d}:00"
                ], className="mb-0 small")
            ], width=4),
            
            dbc.Col([
                html.H6("Regime Distribution", className="mb-2"),
                html.Div([
                    html.P([
                        html.Strong(f"{regime}: "), percentage
                    ], className="mb-1 small")
                    for regime, percentage in regime_distribution_pct.items()
                ])
            ], width=4),
            
            dbc.Col([
                html.H6("Analysis Quality", className="mb-2"),
                html.P([
                    html.Strong("Avg Confidence: "), f"{avg_confidence:.2f}"
                ], className="mb-1 small"),
                html.P([
                    html.Strong("Data Points: "), "24 hours"
                ], className="mb-1 small"),
                html.P([
                    html.Strong("Regime Matches: "), str(len(set(regime_distribution.keys())))
                ], className="mb-0 small")
            ], width=4)
        ])
        
        return footer_content
        
    except Exception as e:
        return html.Div([
            dbc.Alert(f"Error creating footer: {str(e)}", color="danger", className="small")
        ])

# Daily Drill-Down Modal Callbacks
@app.callback(
    [
        Output('daily-drill-modal', 'is_open'),
        Output('selected-date-store', 'data'),
        Output('daily-modal-title', 'children')
    ],
    [
        Input({'type': 'calendar-cell', 'date': dependencies.ALL}, 'n_clicks'),
        Input('daily-modal-close-button', 'n_clicks')
    ],
    [
        State('daily-drill-modal', 'is_open'),
        State('selected-date-store', 'data')
    ]
)

# Weekend status callback
@app.callback(
    Output('weekend-status-display', 'children'),
    [Input('update-interval', 'n_intervals')]
)
def update_weekend_status(n_intervals):
    """Update weekend status display"""
    try:
        status_message = get_weekend_status_message()

        if is_weekend():
            # Weekend mode - show alert style
            return dbc.Alert(
                [
                    html.I(className="fas fa-clock me-2"),
                    status_message
                ],
                color="warning",
                className="mb-0"
            )
        else:
            # Live mode - show success style
            return dbc.Alert(
                [
                    html.I(className="fas fa-broadcast-tower me-2"),
                    status_message
                ],
                color="success",
                className="mb-0"
            )
    except Exception as e:
        return dbc.Alert(
            f"Status check error: {str(e)}",
            color="danger",
            className="mb-0"
        )


if __name__ == '__main__':
    app.run_server(debug=True, host='0.0.0.0', port=8050)