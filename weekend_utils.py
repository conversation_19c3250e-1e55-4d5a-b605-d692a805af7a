"""
Weekend detection and handling utilities for Dynamic FX Clustering Application.
Provides functions to detect weekends and manage data freezing during non-trading hours.
Based on matrix_QP patterns for consistent behavior.
"""

from datetime import datetime, timedelta
import pytz
import dash
from typing import Any, Optional, Dict
import logging

# Configure timezone - should match trading timezone
TRADING_TIMEZONE = pytz.timezone('Europe/Bucharest')

# Configure logging
logger = logging.getLogger(__name__)

# Global cache for Friday data
_friday_data_cache: Dict[str, Dict[str, Any]] = {}

def is_weekend():
    """
    Check if current time is during weekend (Saturday or Sunday).

    Returns:
        bool: True if it's weekend, False otherwise
    """
    now = datetime.now(TRADING_TIMEZONE)
    weekday = now.weekday()
    is_weekend_result = weekday >= 5  # 5 = Saturday, 6 = Sunday
    logger.debug(f"Weekend check: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}, weekday={weekday}, is_weekend={is_weekend_result}")
    return is_weekend_result

def get_last_friday_end(weeks_back=0):
    """
    Get the end time of the last Friday (23:59:59).

    Args:
        weeks_back (int): Number of weeks to go back (0 = most recent Friday)

    Returns:
        datetime: Last Friday's end time in trading timezone
    """
    now = datetime.now(TRADING_TIMEZONE)

    # Calculate days back to Friday
    if now.weekday() == 5:  # Saturday
        days_back = 1
    elif now.weekday() == 6:  # Sunday
        days_back = 2
    else:  # Weekday
        if now.weekday() < 4:  # Monday to Thursday
            days_back = (now.weekday() + 3) % 7  # Go back to previous Friday
        else:  # Friday
            days_back = 0  # Current day is Friday

    # Add additional weeks if requested
    days_back += (weeks_back * 7)

    last_friday = now - timedelta(days=days_back)
    last_friday_end = last_friday.replace(hour=23, minute=59, second=59, microsecond=0)

    return last_friday_end

def get_last_friday_start(weeks_back=0):
    """
    Get the start time of the last Friday (00:00:00).

    Args:
        weeks_back (int): Number of weeks to go back (0 = most recent Friday)

    Returns:
        datetime: Last Friday's start time in trading timezone
    """
    friday_end = get_last_friday_end(weeks_back)
    friday_start = friday_end.replace(hour=0, minute=0, second=0, microsecond=0)
    return friday_start

def should_freeze_updates():
    """
    Determine if updates should be frozen (during weekends).

    Returns:
        bool: True if updates should be frozen, False otherwise
    """
    return is_weekend()

def should_use_friday_data():
    """
    Determine if we should try to use Friday data during weekends.
    This is more lenient than should_freeze_updates - it allows fallback to live data.

    Returns:
        bool: True if we should prefer Friday data, False otherwise
    """
    return is_weekend()

def prevent_weekend_update():
    """
    Dash callback decorator to prevent updates during weekends.
    Use this to automatically prevent callback execution during weekends.
    
    Usage:
        @prevent_weekend_update()
        def my_callback(...):
            # This will not execute during weekends
            pass
    """
    if should_freeze_updates():
        raise dash.exceptions.PreventUpdate

def get_weekend_status_message():
    """
    Get a status message indicating weekend mode.
    
    Returns:
        str: Status message for weekend mode
    """
    if is_weekend():
        last_friday = get_last_friday_end()
        return f"Weekend Mode: Showing Friday's data (last update: {last_friday.strftime('%Y-%m-%d %H:%M:%S')})"
    else:
        return "Live Trading Mode: Real-time updates active"

def get_weekend_time_range(weeks_back=0):
    """
    Get the time range for weekend data (Friday's full day).

    Args:
        weeks_back (int): Number of weeks to go back (0 = most recent Friday)

    Returns:
        tuple: (start_time, end_time) for Friday's data
    """
    if is_weekend():
        friday_start = get_last_friday_start(weeks_back)
        friday_end = get_last_friday_end(weeks_back)
        return friday_start, friday_end
    else:
        # Return current day range for weekdays
        now = datetime.now(TRADING_TIMEZONE)
        day_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        return day_start, now

def get_available_friday_data_range(max_weeks_back=4):
    """
    Find the most recent Friday with potentially available data.
    Tries multiple Fridays going back in time.

    Args:
        max_weeks_back (int): Maximum number of weeks to search back

    Returns:
        tuple: (start_time, end_time, weeks_back) for the Friday to try, or None if none found
    """
    if not is_weekend():
        return get_weekend_time_range() + (0,)

    # Try current Friday first, then go back week by week
    for weeks_back in range(max_weeks_back + 1):
        friday_start = get_last_friday_start(weeks_back)
        friday_end = get_last_friday_end(weeks_back)

        # Skip very recent dates (less than 1 day old) as they might not have complete data
        now = datetime.now(TRADING_TIMEZONE)
        days_since_friday = (now - friday_end).days

        if days_since_friday >= 1 or weeks_back > 0:  # At least 1 day old or not current week
            logger.info(f"Trying Friday data from {friday_start.strftime('%Y-%m-%d')} (weeks back: {weeks_back})")
            return friday_start, friday_end, weeks_back

    # Fallback to most recent Friday if nothing else works
    logger.warning(f"No suitable Friday found in {max_weeks_back} weeks, using most recent")
    friday_start = get_last_friday_start(0)
    friday_end = get_last_friday_end(0)
    return friday_start, friday_end, 0

def get_recent_trading_day_range():
    """
    Get a recent trading day range that's likely to have data.
    Tries the most recent weekday (Monday-Friday) that's at least 1 day old.

    Returns:
        tuple: (start_time, end_time) for a recent trading day
    """
    now = datetime.now(TRADING_TIMEZONE)

    # Go back day by day until we find a weekday that's at least 1 day old
    for days_back in range(1, 15):  # Try up to 2 weeks back
        target_date = now - timedelta(days=days_back)

        # Check if it's a weekday (Monday=0, Friday=4)
        if target_date.weekday() < 5:
            day_start = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = target_date.replace(hour=23, minute=59, second=59, microsecond=0)
            logger.info(f"Using recent trading day: {target_date.strftime('%Y-%m-%d')}")
            return day_start, day_end

    # Fallback to last Friday if no weekday found
    logger.warning("No recent weekday found, falling back to last Friday")
    return get_last_friday_start(1), get_last_friday_end(1)

def get_extended_friday_range(weeks_back=0):
    """
    Get an extended Friday range that includes more data for better availability.
    Instead of just Friday, get Thursday evening to Friday evening.

    Args:
        weeks_back (int): Number of weeks to go back

    Returns:
        tuple: (start_time, end_time) for extended Friday range
    """
    friday_end = get_last_friday_end(weeks_back)
    # Start from Thursday evening (18:00) to get more data
    thursday = friday_end - timedelta(days=1)
    extended_start = thursday.replace(hour=18, minute=0, second=0, microsecond=0)

    logger.info(f"Using extended Friday range: {extended_start} to {friday_end}")
    return extended_start, friday_end

def cache_friday_data(key: str, data: Any):
    """
    Cache Friday data during weekends with smart caching system.

    Args:
        key (str): Cache key identifier
        data: Data to cache
    """
    global _friday_data_cache
    
    if not is_weekend():
        # During trading days, cache data for potential weekend use
        _friday_data_cache[key] = {
            'data': data,
            'timestamp': datetime.now(TRADING_TIMEZONE),
            'friday_end': get_last_friday_end()
        }
        logger.debug(f"Data cached for weekend use, key: {key}")
    else:
        logger.debug(f"Not caching data during weekend for key: {key}")

def get_cached_friday_data(key: str):
    """
    Get cached Friday data during weekends with smart caching system.

    Args:
        key (str): Cache key identifier

    Returns:
        Cached data if available and valid, None otherwise
    """
    global _friday_data_cache

    weekend_check = is_weekend()
    if not weekend_check:
        logger.debug(f"Not using cache during trading days for key: {key}")
        return None  # Don't use cache during trading days

    if key not in _friday_data_cache:
        logger.debug(f"No cached data found for key: {key}")
        return None

    cached_item = _friday_data_cache[key]
    cached_friday_end = cached_item.get('friday_end')
    current_friday_end = get_last_friday_end()

    # Check if cached data is from the current Friday
    if cached_friday_end and cached_friday_end.date() == current_friday_end.date():
        logger.debug(f"Using cached Friday data for key: {key}")
        return cached_item['data']
    else:
        logger.debug(f"Cached data is stale for key: {key}, clearing cache")
        del _friday_data_cache[key]
        return None

def clear_weekend_cache():
    """
    Clear the weekend data cache.
    Useful for cleanup or when starting a new trading week.
    """
    global _friday_data_cache
    _friday_data_cache.clear()
    logger.info("Weekend data cache cleared")

def log_weekend_status():
    """
    Log current weekend status for debugging.
    """
    status = get_weekend_status_message()
    if is_weekend():
        friday_start, friday_end = get_weekend_time_range()
        logger.info(f"{status}")
        logger.info(f"Friday data range: {friday_start} to {friday_end}")
    else:
        logger.info(f"{status}")

def is_new_trading_week():
    """
    Check if this is the start of a new trading week (Monday).
    
    Returns:
        bool: True if it's Monday, False otherwise
    """
    now = datetime.now(TRADING_TIMEZONE)
    return now.weekday() == 0  # 0 = Monday

# Auto-clear cache on new trading week
if is_new_trading_week() and not is_weekend():
    clear_weekend_cache()
    logger.info("New trading week detected, cache cleared")
