"""
Weekend detection and handling utilities for Dynamic FX Clustering Application.
Provides functions to detect weekends and manage data freezing during non-trading hours.
Based on matrix_QP patterns for consistent behavior.
"""

from datetime import datetime, timedelta
import pytz
import dash
from typing import Any, Optional, Dict
import logging

# Configure timezone - should match trading timezone
TRADING_TIMEZONE = pytz.timezone('Europe/Bucharest')

# Configure logging
logger = logging.getLogger(__name__)

# Global cache for Friday data
_friday_data_cache: Dict[str, Dict[str, Any]] = {}

def is_weekend():
    """
    Check if current time is during weekend (Saturday or Sunday).

    Returns:
        bool: True if it's weekend, False otherwise
    """
    now = datetime.now(TRADING_TIMEZONE)
    weekday = now.weekday()
    is_weekend_result = weekday >= 5  # 5 = Saturday, 6 = Sunday
    logger.debug(f"Weekend check: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}, weekday={weekday}, is_weekend={is_weekend_result}")
    return is_weekend_result

def get_last_friday_end():
    """
    Get the end time of the last Friday (23:59:59).
    
    Returns:
        datetime: Last Friday's end time in trading timezone
    """
    now = datetime.now(TRADING_TIMEZONE)
    
    # Calculate days back to Friday
    if now.weekday() == 5:  # Saturday
        days_back = 1
    elif now.weekday() == 6:  # Sunday
        days_back = 2
    else:  # Weekday
        if now.weekday() < 4:  # Monday to Thursday
            days_back = (now.weekday() + 3) % 7  # Go back to previous Friday
        else:  # Friday
            days_back = 0  # Current day is Friday
    
    last_friday = now - timedelta(days=days_back)
    last_friday_end = last_friday.replace(hour=23, minute=59, second=59, microsecond=0)
    
    return last_friday_end

def get_last_friday_start():
    """
    Get the start time of the last Friday (00:00:00).
    
    Returns:
        datetime: Last Friday's start time in trading timezone
    """
    friday_end = get_last_friday_end()
    friday_start = friday_end.replace(hour=0, minute=0, second=0, microsecond=0)
    return friday_start

def should_freeze_updates():
    """
    Determine if updates should be frozen (during weekends).

    Returns:
        bool: True if updates should be frozen, False otherwise
    """
    return is_weekend()

def should_use_friday_data():
    """
    Determine if we should try to use Friday data during weekends.
    This is more lenient than should_freeze_updates - it allows fallback to live data.

    Returns:
        bool: True if we should prefer Friday data, False otherwise
    """
    return is_weekend()

def prevent_weekend_update():
    """
    Dash callback decorator to prevent updates during weekends.
    Use this to automatically prevent callback execution during weekends.
    
    Usage:
        @prevent_weekend_update()
        def my_callback(...):
            # This will not execute during weekends
            pass
    """
    if should_freeze_updates():
        raise dash.exceptions.PreventUpdate

def get_weekend_status_message():
    """
    Get a status message indicating weekend mode.
    
    Returns:
        str: Status message for weekend mode
    """
    if is_weekend():
        last_friday = get_last_friday_end()
        return f"Weekend Mode: Showing Friday's data (last update: {last_friday.strftime('%Y-%m-%d %H:%M:%S')})"
    else:
        return "Live Trading Mode: Real-time updates active"

def get_weekend_time_range():
    """
    Get the time range for weekend data (Friday's full day).
    
    Returns:
        tuple: (start_time, end_time) for Friday's data
    """
    if is_weekend():
        friday_start = get_last_friday_start()
        friday_end = get_last_friday_end()
        return friday_start, friday_end
    else:
        # Return current day range for weekdays
        now = datetime.now(TRADING_TIMEZONE)
        day_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        return day_start, now

def cache_friday_data(key: str, data: Any):
    """
    Cache Friday data during weekends with smart caching system.

    Args:
        key (str): Cache key identifier
        data: Data to cache
    """
    global _friday_data_cache
    
    if not is_weekend():
        # During trading days, cache data for potential weekend use
        _friday_data_cache[key] = {
            'data': data,
            'timestamp': datetime.now(TRADING_TIMEZONE),
            'friday_end': get_last_friday_end()
        }
        logger.debug(f"Data cached for weekend use, key: {key}")
    else:
        logger.debug(f"Not caching data during weekend for key: {key}")

def get_cached_friday_data(key: str):
    """
    Get cached Friday data during weekends with smart caching system.

    Args:
        key (str): Cache key identifier

    Returns:
        Cached data if available and valid, None otherwise
    """
    global _friday_data_cache

    weekend_check = is_weekend()
    if not weekend_check:
        logger.debug(f"Not using cache during trading days for key: {key}")
        return None  # Don't use cache during trading days

    if key not in _friday_data_cache:
        logger.debug(f"No cached data found for key: {key}")
        return None

    cached_item = _friday_data_cache[key]
    cached_friday_end = cached_item.get('friday_end')
    current_friday_end = get_last_friday_end()

    # Check if cached data is from the current Friday
    if cached_friday_end and cached_friday_end.date() == current_friday_end.date():
        logger.debug(f"Using cached Friday data for key: {key}")
        return cached_item['data']
    else:
        logger.debug(f"Cached data is stale for key: {key}, clearing cache")
        del _friday_data_cache[key]
        return None

def clear_weekend_cache():
    """
    Clear the weekend data cache.
    Useful for cleanup or when starting a new trading week.
    """
    global _friday_data_cache
    _friday_data_cache.clear()
    logger.info("Weekend data cache cleared")

def log_weekend_status():
    """
    Log current weekend status for debugging.
    """
    status = get_weekend_status_message()
    if is_weekend():
        friday_start, friday_end = get_weekend_time_range()
        logger.info(f"{status}")
        logger.info(f"Friday data range: {friday_start} to {friday_end}")
    else:
        logger.info(f"{status}")

def is_new_trading_week():
    """
    Check if this is the start of a new trading week (Monday).
    
    Returns:
        bool: True if it's Monday, False otherwise
    """
    now = datetime.now(TRADING_TIMEZONE)
    return now.weekday() == 0  # 0 = Monday

# Auto-clear cache on new trading week
if is_new_trading_week() and not is_weekend():
    clear_weekend_cache()
    logger.info("New trading week detected, cache cleared")
