{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"resolve-config\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"python3-dll-a\", \"resolve-config\"]", "target": 1021922240164113426, "profile": 2225463790103693989, "path": 14053354235032328853, "deps": [[3722963349756955755, "once_cell", false, 15031127281168514016], [3987260608810180118, "build_script_build", false, 5235732327338413388], [10296317077653712691, "target_lexicon", false, 10479552859226511615]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-build-config-1981d5b3ee3762d8\\dep-lib-pyo3_build_config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}