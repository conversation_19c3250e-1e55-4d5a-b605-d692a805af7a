{"$message_type":"diagnostic","message":"unused import: `PyArray1`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":80,"byte_end":88,"line_start":3,"line_end":3,"column_start":23,"column_end":31,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":23,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":78,"byte_end":88,"line_start":3,"line_end":3,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":69,"byte_end":70,"line_start":3,"line_end":3,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":88,"byte_end":89,"line_start":3,"line_end":3,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"use numpy::{PyArray2, PyArray1};","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `PyArray1`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:3:23\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse numpy::{PyArray2, PyArray1};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime`, `NaiveDateTime`, `Timelike`, and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":255,"byte_end":263,"line_start":9,"line_end":9,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":265,"byte_end":268,"line_start":9,"line_end":9,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":270,"byte_end":283,"line_start":9,"line_end":9,"column_start":29,"column_end":42,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":29,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":285,"byte_end":293,"line_start":9,"line_end":9,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":242,"byte_end":296,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDateTime, Timelike};","highlight_start":1,"highlight_end":54},{"text":"use linfa::prelude::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `DateTime`, `NaiveDateTime`, `Timelike`, and `Utc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:9:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc, NaiveDateTime, Timelike};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `KMeansParams`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":350,"byte_end":362,"line_start":11,"line_end":11,"column_start":32,"column_end":44,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":32,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":348,"byte_end":362,"line_start":11,"line_end":11,"column_start":30,"column_end":44,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":30,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":341,"byte_end":342,"line_start":11,"line_end":11,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":362,"byte_end":363,"line_start":11,"line_end":11,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"use linfa_clustering::{KMeans, KMeansParams};","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `KMeansParams`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:11:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse linfa_clustering::{KMeans, KMeansParams};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `cluster_volatility_profiles` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":33836,"byte_end":33863,"line_start":1062,"line_end":1062,"column_start":8,"column_end":35,"is_primary":true,"text":[{"text":"pub fn cluster_volatility_profiles(","highlight_start":8,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `cluster_volatility_profiles` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1062:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1062\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn cluster_volatility_profiles(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `KMeansResult` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":39136,"byte_end":39148,"line_start":1208,"line_end":1208,"column_start":8,"column_end":20,"is_primary":true,"text":[{"text":"struct KMeansResult {","highlight_start":8,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `KMeansResult` is never constructed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1208:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1208\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct KMeansResult {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `NormalizationStats` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":39380,"byte_end":39398,"line_start":1220,"line_end":1220,"column_start":8,"column_end":26,"is_primary":true,"text":[{"text":"struct NormalizationStats {","highlight_start":8,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `NormalizationStats` is never constructed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1220:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1220\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct NormalizationStats {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `create_single_cluster_result` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":39513,"byte_end":39541,"line_start":1226,"line_end":1226,"column_start":4,"column_end":32,"is_primary":true,"text":[{"text":"fn create_single_cluster_result(","highlight_start":4,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `create_single_cluster_result` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1226:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn create_single_cluster_result(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `validate_volatility_vector` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":41591,"byte_end":41617,"line_start":1294,"line_end":1294,"column_start":4,"column_end":30,"is_primary":true,"text":[{"text":"fn validate_volatility_vector(vector: &DailyVolatilityVector) -> PyResult<bool> {","highlight_start":4,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `validate_volatility_vector` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1294:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn validate_volatility_vector(vector: &DailyVolatilityVector) -> PyResult<bool> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `vectors_to_matrix` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":43031,"byte_end":43048,"line_start":1341,"line_end":1341,"column_start":4,"column_end":21,"is_primary":true,"text":[{"text":"fn vectors_to_matrix(vectors: &[&DailyVolatilityVector]) -> PyResult<Array2<f64>> {","highlight_start":4,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `vectors_to_matrix` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1341:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1341\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn vectors_to_matrix(vectors: &[&DailyVolatilityVector]) -> PyResult<Array2<f64>> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `normalize_volatility_data` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":44168,"byte_end":44193,"line_start":1373,"line_end":1373,"column_start":4,"column_end":29,"is_primary":true,"text":[{"text":"fn normalize_volatility_data(data: &Array2<f64>) -> PyResult<(Array2<f64>, NormalizationStats)> {","highlight_start":4,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `normalize_volatility_data` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1373:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn normalize_volatility_data(data: &Array2<f64>) -> PyResult<(Array2<f64>, NormalizationStats)> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `perform_kmeans_clustering` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":45242,"byte_end":45267,"line_start":1404,"line_end":1404,"column_start":4,"column_end":29,"is_primary":true,"text":[{"text":"fn perform_kmeans_clustering(","highlight_start":4,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `perform_kmeans_clustering` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1404:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1404\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn perform_kmeans_clustering(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `calculate_volatility_silhouette_score` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":47318,"byte_end":47355,"line_start":1472,"line_end":1472,"column_start":4,"column_end":41,"is_primary":true,"text":[{"text":"fn calculate_volatility_silhouette_score(","highlight_start":4,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `calculate_volatility_silhouette_score` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1472:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1472\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn calculate_volatility_silhouette_score(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `calculate_calinski_harabasz_score` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":49625,"byte_end":49658,"line_start":1542,"line_end":1542,"column_start":4,"column_end":37,"is_primary":true,"text":[{"text":"fn calculate_calinski_harabasz_score(","highlight_start":4,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `calculate_calinski_harabasz_score` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1542:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn calculate_calinski_harabasz_score(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `euclidean_distance` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":51598,"byte_end":51616,"line_start":1603,"line_end":1603,"column_start":4,"column_end":22,"is_primary":true,"text":[{"text":"fn euclidean_distance(point1: &ndarray::ArrayView1<f64>, point2: &ndarray::ArrayView1<f64>) -> f64 {","highlight_start":4,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `euclidean_distance` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1603:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1603\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn euclidean_distance(point1: &ndarray::ArrayView1<f64>, point2: &ndarray::ArrayView1<f64>) -> f64 {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `create_volatility_archetypes` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":51875,"byte_end":51903,"line_start":1612,"line_end":1612,"column_start":4,"column_end":32,"is_primary":true,"text":[{"text":"fn create_volatility_archetypes(","highlight_start":4,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `create_volatility_archetypes` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1612:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1612\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn create_volatility_archetypes(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `find_peak_and_quiet_hours` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":54062,"byte_end":54087,"line_start":1677,"line_end":1677,"column_start":4,"column_end":29,"is_primary":true,"text":[{"text":"fn find_peak_and_quiet_hours(hourly_pattern: &[f64]) -> (usize, usize) {","highlight_start":4,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `find_peak_and_quiet_hours` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1677:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1677\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn find_peak_and_quiet_hours(hourly_pattern: &[f64]) -> (usize, usize) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `calculate_intra_cluster_coherence` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":54602,"byte_end":54635,"line_start":1698,"line_end":1698,"column_start":4,"column_end":37,"is_primary":true,"text":[{"text":"fn calculate_intra_cluster_coherence(","highlight_start":4,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `calculate_intra_cluster_coherence` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1698:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1698\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn calculate_intra_cluster_coherence(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `calculate_vector_correlation` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":55473,"byte_end":55501,"line_start":1728,"line_end":1728,"column_start":4,"column_end":32,"is_primary":true,"text":[{"text":"fn calculate_vector_correlation(vec1: &[f64], vec2: &[f64]) -> PyResult<f64> {","highlight_start":4,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `calculate_vector_correlation` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1728:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1728\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn calculate_vector_correlation(vec1: &[f64], vec2: &[f64]) -> PyResult<f64> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `classify_regime_pattern` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":56413,"byte_end":56436,"line_start":1763,"line_end":1763,"column_start":4,"column_end":27,"is_primary":true,"text":[{"text":"fn classify_regime_pattern(hourly_pattern: &[f64], peak_hour: usize, _quiet_hour: usize) -> String {","highlight_start":4,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `classify_regime_pattern` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1763:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1763\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn classify_regime_pattern(hourly_pattern: &[f64], peak_hour: usize, _quiet_hour: usize) -> String {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1586,"line_start":61,"line_end":61,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":1604,"byte_end":1614,"line_start":62,"line_end":62,"column_start":6,"column_end":16,"is_primary":false,"text":[{"text":"impl LogReturns {","highlight_start":6,"highlight_end":16}],"label":"`LogReturns` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1586,"line_start":61,"line_end":61,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":1604,"byte_end":1614,"line_start":62,"line_end":62,"column_start":6,"column_end":16,"is_primary":false,"text":[{"text":"impl LogReturns {","highlight_start":6,"highlight_end":16}],"label":"`LogReturns` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1586,"byte_end":1598,"line_start":61,"line_end":61,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(non_local_definitions)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:61:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl LogReturns {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`LogReturns` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`LogReturns` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_local_definitions)]` on by default\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2084,"line_start":85,"line_end":85,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2102,"byte_end":2119,"line_start":86,"line_end":86,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl CorrelationMatrix {","highlight_start":6,"highlight_end":23}],"label":"`CorrelationMatrix` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2084,"line_start":85,"line_end":85,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2102,"byte_end":2119,"line_start":86,"line_end":86,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl CorrelationMatrix {","highlight_start":6,"highlight_end":23}],"label":"`CorrelationMatrix` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2084,"byte_end":2096,"line_start":85,"line_end":85,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:85:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CorrelationMatrix {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`CorrelationMatrix` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`CorrelationMatrix` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3126,"line_start":131,"line_end":131,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":3144,"byte_end":3161,"line_start":132,"line_end":132,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl ClusterStatistics {","highlight_start":6,"highlight_end":23}],"label":"`ClusterStatistics` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3126,"line_start":131,"line_end":131,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":3144,"byte_end":3161,"line_start":132,"line_end":132,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl ClusterStatistics {","highlight_start":6,"highlight_end":23}],"label":"`ClusterStatistics` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3126,"byte_end":3138,"line_start":131,"line_end":131,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:131:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ClusterStatistics {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusterStatistics` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusterStatistics` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5734,"line_start":246,"line_end":246,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":5752,"byte_end":5771,"line_start":247,"line_end":247,"column_start":6,"column_end":25,"is_primary":false,"text":[{"text":"impl VolatilityArchetype {","highlight_start":6,"highlight_end":25}],"label":"`VolatilityArchetype` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5734,"line_start":246,"line_end":246,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":5752,"byte_end":5771,"line_start":247,"line_end":247,"column_start":6,"column_end":25,"is_primary":false,"text":[{"text":"impl VolatilityArchetype {","highlight_start":6,"highlight_end":25}],"label":"`VolatilityArchetype` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":5734,"byte_end":5746,"line_start":246,"line_end":246,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:246:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m246\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m247\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl VolatilityArchetype {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityArchetype` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityArchetype` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6711,"line_start":278,"line_end":278,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":6729,"byte_end":6751,"line_start":279,"line_end":279,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl VolatilityRegimeResult {","highlight_start":6,"highlight_end":28}],"label":"`VolatilityRegimeResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6711,"line_start":278,"line_end":278,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":6729,"byte_end":6751,"line_start":279,"line_end":279,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl VolatilityRegimeResult {","highlight_start":6,"highlight_end":28}],"label":"`VolatilityRegimeResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6711,"byte_end":6723,"line_start":278,"line_end":278,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:278:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl VolatilityRegimeResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityRegimeResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityRegimeResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8205,"line_start":318,"line_end":318,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":8223,"byte_end":8244,"line_start":319,"line_end":319,"column_start":6,"column_end":27,"is_primary":false,"text":[{"text":"impl DailyVolatilityVector {","highlight_start":6,"highlight_end":27}],"label":"`DailyVolatilityVector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8205,"line_start":318,"line_end":318,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":8223,"byte_end":8244,"line_start":319,"line_end":319,"column_start":6,"column_end":27,"is_primary":false,"text":[{"text":"impl DailyVolatilityVector {","highlight_start":6,"highlight_end":27}],"label":"`DailyVolatilityVector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":8205,"byte_end":8217,"line_start":318,"line_end":318,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:318:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m318\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DailyVolatilityVector {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9702,"line_start":364,"line_end":364,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":9720,"byte_end":9742,"line_start":365,"line_end":365,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl DailyVolatilityVectors {","highlight_start":6,"highlight_end":28}],"label":"`DailyVolatilityVectors` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9702,"line_start":364,"line_end":364,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":9720,"byte_end":9742,"line_start":365,"line_end":365,"column_start":6,"column_end":28,"is_primary":false,"text":[{"text":"impl DailyVolatilityVectors {","highlight_start":6,"highlight_end":28}],"label":"`DailyVolatilityVectors` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":9702,"byte_end":9714,"line_start":364,"line_end":364,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:364:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m365\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DailyVolatilityVectors {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVectors` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`DailyVolatilityVectors` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18937,"line_start":620,"line_end":620,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18949,"line_start":620,"line_end":620,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":18955,"byte_end":18966,"line_start":621,"line_end":621,"column_start":6,"column_end":17,"is_primary":false,"text":[{"text":"impl FxPriceData {","highlight_start":6,"highlight_end":17}],"label":"`FxPriceData` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18937,"line_start":620,"line_end":620,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18949,"line_start":620,"line_end":620,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":18955,"byte_end":18966,"line_start":621,"line_end":621,"column_start":6,"column_end":17,"is_primary":false,"text":[{"text":"impl FxPriceData {","highlight_start":6,"highlight_end":17}],"label":"`FxPriceData` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18949,"line_start":620,"line_end":620,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18949,"line_start":620,"line_end":620,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18949,"line_start":620,"line_end":620,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":18937,"byte_end":18949,"line_start":620,"line_end":620,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:620:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m620\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m621\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl FxPriceData {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`FxPriceData` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`FxPriceData` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19138,"line_start":632,"line_end":632,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19150,"line_start":632,"line_end":632,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19156,"byte_end":19172,"line_start":633,"line_end":633,"column_start":6,"column_end":22,"is_primary":false,"text":[{"text":"impl ClusteringResult {","highlight_start":6,"highlight_end":22}],"label":"`ClusteringResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19138,"line_start":632,"line_end":632,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19150,"line_start":632,"line_end":632,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19156,"byte_end":19172,"line_start":633,"line_end":633,"column_start":6,"column_end":22,"is_primary":false,"text":[{"text":"impl ClusteringResult {","highlight_start":6,"highlight_end":22}],"label":"`ClusteringResult` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19150,"line_start":632,"line_end":632,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19150,"line_start":632,"line_end":632,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19150,"line_start":632,"line_end":632,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19138,"byte_end":19150,"line_start":632,"line_end":632,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:632:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m632\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m633\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ClusteringResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusteringResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`ClusteringResult` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19404,"line_start":645,"line_end":645,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassImplCollector` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19416,"line_start":645,"line_end":645,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19422,"byte_end":19439,"line_start":646,"line_end":646,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl VolatilityProfile {","highlight_start":6,"highlight_end":23}],"label":"`VolatilityProfile` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19404,"line_start":645,"line_end":645,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":1}],"label":"`PyClassNewTextSignature` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19416,"line_start":645,"line_end":645,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19422,"byte_end":19439,"line_start":646,"line_end":646,"column_start":6,"column_end":23,"is_primary":false,"text":[{"text":"impl VolatilityProfile {","highlight_start":6,"highlight_end":23}],"label":"`VolatilityProfile` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19416,"line_start":645,"line_end":645,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":"move the `impl` block outside of this function `trampoline` and up 4 bodies","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19416,"line_start":645,"line_end":645,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19416,"line_start":645,"line_end":645,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":19404,"byte_end":19416,"line_start":645,"line_end":645,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[pymethods]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[pymethods]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pyo3-macros-0.19.2\\src\\lib.rs","byte_start":5108,"byte_end":5178,"line_start":111,"line_end":111,"column_start":1,"column_end":71,"is_primary":false,"text":[{"text":"pub fn pymethods(attr: TokenStream, input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:645:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m645\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[pymethods]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassImplCollector` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`PyClassNewTextSignature` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this function `trampoline` and up 4 bodies\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m646\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl VolatilityProfile {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityProfile` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`VolatilityProfile` is not local\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the attribute macro `pymethods` may come from an old version of the `pyo3_macros` crate, try updating your dependency with `cargo update -p pyo3_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `pymethods` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"29 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 29 warnings emitted\u001b[0m\n\n"}
