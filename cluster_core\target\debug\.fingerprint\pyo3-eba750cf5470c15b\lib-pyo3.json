{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"default\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"unindent\"]", "target": 230425422904717816, "profile": 2241668132362809309, "path": 16362659447176189382, "deps": [[2828590642173593838, "cfg_if", false, 10118896523543894662], [3331198645124635644, "indoc", false, 10693410716207793265], [4495526598637097934, "parking_lot", false, 12687993455921991946], [4684437522915235464, "libc", false, 9685844023081248841], [9128542196482641081, "pyo3_ffi", false, 4846787767608039638], [9830651184186693607, "build_script_build", false, 4023051120568877181], [12920613885498201104, "pyo3_macros", false, 12810223196973300321], [14643204177830147187, "memoffset", false, 995327542446860875], [18292562650629842117, "unindent", false, 8192468416972037468]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-eba750cf5470c15b\\dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}