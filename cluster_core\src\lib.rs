use pyo3::prelude::*;
use pyo3::exceptions::PyValueError;
use numpy::{PyArray2, PyArray1};
use ndarray::{Array2, Array1};
use serde::{Serialize, Deserialize};
use log::{info, warn, error};
use thiserror::Error;
use std::collections::HashMap;
use chrono::{DateTime, Utc, NaiveDateTime, Timelike};
use linfa::prelude::*;
use linfa_clustering::{KMeans, KMeansParams};

#[derive(Error, Debug)]
pub enum CalculationError {
    #[error("Invalid price data: {0}")]
    InvalidPrice(String),
    #[error("Missing data points")]
    MissingData,
    #[error("Constant time series detected: {0}")]
    ConstantTimeSeries(String),
    #[error("Insufficient data points for correlation")]
    InsufficientData,
    #[error("Invalid correlation matrix: {0}")]
    InvalidCorrelationMatrix(String),
    #[error("Clustering error: {0}")]
    ClusteringError(String),
    #[error("Timezone error: {0}")]
    TimezoneError(String),
    #[error("Volatility calculation error: {0}")]
    VolatilityError(String),
    #[error("Invalid date range: {0}")]
    InvalidDateRange(String),
}

impl From<CalculationError> for PyErr {
    fn from(err: CalculationError) -> PyErr {
        PyValueError::new_err(err.to_string())
    }
}


#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FxPriceData {
    #[pyo3(get, set)]
    pub prices: Vec<f64>,
    #[pyo3(get, set)]
    pub symbols: Vec<String>,
    #[pyo3(get, set)]
    pub timestamps: Vec<i64>,
}

#[pyclass]
#[derive(Debug, Clone)]
pub struct LogReturns {
    returns_array: Array2<f64>,
    #[pyo3(get)]
    symbols: Vec<String>,
}

#[pymethods]
impl LogReturns {
    #[new]
    fn new() -> Self {
        Self {
            returns_array: Array2::<f64>::zeros((0, 0)),
            symbols: Vec::new(),
        }
    }

    #[getter]
    fn returns<'py>(&'py self, py: Python<'py>) -> PyResult<&'py PyArray2<f64>> {
        Ok(PyArray2::from_array(py, &self.returns_array))
    }
}

#[pyclass]
#[derive(Debug, Clone)]
pub struct CorrelationMatrix {
    correlation_array: Array2<f64>,
    #[pyo3(get)]
    symbols: Vec<String>,
}

#[pymethods]
impl CorrelationMatrix {
    #[new]
    fn new() -> Self {
        Self {
            correlation_array: Array2::<f64>::zeros((0, 0)),
            symbols: Vec::new(),
        }
    }

    #[getter]
    fn correlation<'py>(&'py self, py: Python<'py>) -> PyResult<&'py PyArray2<f64>> {
        Ok(PyArray2::from_array(py, &self.correlation_array))
    }
}

#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClusteringResult {
    #[pyo3(get)]
    pub cluster_assignments: Vec<usize>,
    #[pyo3(get)]
    pub linkage_matrix: Vec<Vec<f64>>,
    #[pyo3(get)]
    pub silhouette_score: f64,
    #[pyo3(get)]
    pub cophenetic_correlation: f64,
}

#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClusterStatistics {
    #[pyo3(get)]
    pub cluster_id: i32,
    #[pyo3(get)]
    pub members: Vec<String>,
    #[pyo3(get)]
    pub avg_intra_correlation: f64,
    #[pyo3(get)]
    pub avg_volatility: f64,
    #[pyo3(get)]
    pub size: usize,
    #[pyo3(get)]
    pub cohesion_score: f64,
}

#[pymethods]
impl ClusterStatistics {
    #[new]
    fn new(cluster_id: i32) -> Self {
        Self {
            cluster_id,
            members: Vec::new(),
            avg_intra_correlation: 0.0,
            avg_volatility: 0.0,
            size: 0,
            cohesion_score: 0.0,
        }
    }
}

#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityProfile {
    #[pyo3(get)]
    pub hourly_volatility: Vec<f64>,
    #[pyo3(get)]
    pub symbol: String,
    #[pyo3(get)]
    pub mean_volatility: f64,
}

#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DailyVolatilityVector {
    #[pyo3(get)]
    pub date: String,
    #[pyo3(get)]
    pub timestamp: i64,
    #[pyo3(get)]
    pub symbol: String,
    #[pyo3(get)]
    pub hourly_volatility: Vec<f64>,
    #[pyo3(get)]
    pub mean_volatility: f64,
    #[pyo3(get)]
    pub std_volatility: f64,
    #[pyo3(get)]
    pub peak_hour: usize,
    #[pyo3(get)]
    pub quiet_hour: usize,
    #[pyo3(get)]
    pub data_completeness: f64,
}

#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DailyVolatilityVectors {
    #[pyo3(get)]
    pub vectors: Vec<DailyVolatilityVector>,
    #[pyo3(get)]
    pub symbols: Vec<String>,
    #[pyo3(get)]
    pub date_range: Vec<String>,
    #[pyo3(get)]
    pub total_days: usize,
    #[pyo3(get)]
    pub avg_data_completeness: f64,
    #[pyo3(get)]
    pub timezone: String,
}

#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityArchetype {
    #[pyo3(get)]
    pub regime_id: usize,
    #[pyo3(get)]
    pub hourly_pattern: Vec<f64>,
    #[pyo3(get)]
    pub mean_volatility: f64,
    #[pyo3(get)]
    pub std_volatility: f64,
    #[pyo3(get)]
    pub peak_hour: usize,
    #[pyo3(get)]
    pub quiet_hour: usize,
    #[pyo3(get)]
    pub regime_name: String,
    #[pyo3(get)]
    pub member_count: usize,
    #[pyo3(get)]
    pub intra_regime_coherence: f64,
}

#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityRegimeResult {
    #[pyo3(get)]
    pub cluster_assignments: Vec<usize>,
    #[pyo3(get)]
    pub archetypes: Vec<VolatilityArchetype>,
    #[pyo3(get)]
    pub n_clusters: usize,
    #[pyo3(get)]
    pub within_cluster_sum_of_squares: f64,
    #[pyo3(get)]
    pub total_sum_of_squares: f64,
    #[pyo3(get)]
    pub silhouette_score: f64,
    #[pyo3(get)]
    pub calinski_harabasz_score: f64,
    #[pyo3(get)]
    pub inertia: f64,
    #[pyo3(get)]
    pub n_iterations: usize,
    #[pyo3(get)]
    pub converged: bool,
    #[pyo3(get)]
    pub processing_time_ms: u64,
}
#[pymethods]
impl VolatilityArchetype {
    #[new]
    fn new(regime_id: usize) -> Self {
        Self {
            regime_id,
            hourly_pattern: vec![0.0; 24],
            mean_volatility: 0.0,
            std_volatility: 0.0,
            peak_hour: 0,
            quiet_hour: 0,
            regime_name: format!("Regime_{}", regime_id),
            member_count: 0,
            intra_regime_coherence: 0.0,
        }
    }
    
    fn get_pattern_description(&self) -> String {
        let peak_time = format!("{:02}:00", self.peak_hour);
        let quiet_time = format!("{:02}:00", self.quiet_hour);
        format!("Peak at {}, Quiet at {}, Mean Vol: {:.4}", 
                peak_time, quiet_time, self.mean_volatility)
    }
    
    fn get_volatility_at_hour(&self, hour: usize) -> PyResult<f64> {
        if hour >= 24 {
            return Err(PyValueError::new_err("Hour must be between 0 and 23"));
        }
        Ok(self.hourly_pattern[hour])
    }
}

#[pymethods]
impl VolatilityRegimeResult {
    #[new]
    fn new() -> Self {
        Self {
            cluster_assignments: Vec::new(),
            archetypes: Vec::new(),
            n_clusters: 0,
            within_cluster_sum_of_squares: 0.0,
            total_sum_of_squares: 0.0,
            silhouette_score: 0.0,
            calinski_harabasz_score: 0.0,
            inertia: 0.0,
            n_iterations: 0,
            converged: false,
            processing_time_ms: 0,
        }
    }
    
    fn get_cluster_summary(&self) -> HashMap<String, f64> {
        let mut summary = HashMap::new();
        summary.insert("n_clusters".to_string(), self.n_clusters as f64);
        summary.insert("silhouette_score".to_string(), self.silhouette_score);
        summary.insert("wcss".to_string(), self.within_cluster_sum_of_squares);
        summary.insert("calinski_harabasz_score".to_string(), self.calinski_harabasz_score);
        summary.insert("inertia".to_string(), self.inertia);
        summary.insert("converged".to_string(), if self.converged { 1.0 } else { 0.0 });
        summary.insert("processing_time_ms".to_string(), self.processing_time_ms as f64);
        summary
    }
    
    fn get_regime_by_id(&self, regime_id: usize) -> PyResult<VolatilityArchetype> {
        self.archetypes.iter()
            .find(|arch| arch.regime_id == regime_id)
            .cloned()
            .ok_or_else(|| PyValueError::new_err(format!("Regime {} not found", regime_id)))
    }
}


#[pymethods]
impl DailyVolatilityVector {
    #[new]
    fn new(symbol: String, date: String, timestamp: i64) -> Self {
        Self {
            date,
            timestamp,
            symbol,
            hourly_volatility: vec![0.0; 24],
            mean_volatility: 0.0,
            std_volatility: 0.0,
            peak_hour: 0,
            quiet_hour: 0,
            data_completeness: 0.0,
        }
    }
    
    fn get_volatility_at_hour(&self, hour: usize) -> PyResult<f64> {
        if hour >= 24 {
            return Err(PyValueError::new_err("Hour must be between 0 and 23"));
        }
        Ok(self.hourly_volatility[hour])
    }
    
    fn get_volatility_statistics(&self) -> PyResult<(f64, f64, f64, f64)> {
        let valid_values: Vec<f64> = self.hourly_volatility.iter()
            .filter(|&&x| !x.is_nan() && x > 0.0)
            .cloned()
            .collect();
            
        if valid_values.is_empty() {
            return Ok((0.0, 0.0, 0.0, 0.0));
        }
        
        let mean = valid_values.iter().sum::<f64>() / valid_values.len() as f64;
        let variance = valid_values.iter()
            .map(|&x| (x - mean).powi(2))
            .sum::<f64>() / valid_values.len() as f64;
        let std_dev = variance.sqrt();
        let min_val = valid_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_val = valid_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        
        Ok((mean, std_dev, min_val, max_val))
    }
}

#[pymethods]
impl DailyVolatilityVectors {
    #[new]
    fn new() -> Self {
        Self {
            vectors: Vec::new(),
            symbols: Vec::new(),
            date_range: Vec::new(),
            total_days: 0,
            avg_data_completeness: 0.0,
            timezone: "UTC".to_string(),
        }
    }
    
    fn get_vectors_for_symbol(&self, symbol: &str) -> PyResult<Vec<DailyVolatilityVector>> {
        let filtered: Vec<DailyVolatilityVector> = self.vectors.iter()
            .filter(|v| v.symbol == symbol)
            .cloned()
            .collect();
        Ok(filtered)
    }
    
    fn get_vectors_for_date(&self, date: &str) -> PyResult<Vec<DailyVolatilityVector>> {
        let filtered: Vec<DailyVolatilityVector> = self.vectors.iter()
            .filter(|v| v.date == date)
            .cloned()
            .collect();
        Ok(filtered)
    }
    
    fn get_summary_statistics(&self) -> PyResult<HashMap<String, f64>> {
        let mut stats = HashMap::new();
        
        if self.vectors.is_empty() {
            return Ok(stats);
        }
        
        let total_completeness: f64 = self.vectors.iter()
            .map(|v| v.data_completeness)
            .sum();
        let avg_completeness = total_completeness / self.vectors.len() as f64;
        
        let mean_volatilities: Vec<f64> = self.vectors.iter()
            .map(|v| v.mean_volatility)
            .filter(|&x| !x.is_nan())
            .collect();
            
        let overall_mean = if !mean_volatilities.is_empty() {
            mean_volatilities.iter().sum::<f64>() / mean_volatilities.len() as f64
        } else {
            0.0
        };
        
        let overall_std = if mean_volatilities.len() > 1 {
            let variance = mean_volatilities.iter()
                .map(|&x| (x - overall_mean).powi(2))
                .sum::<f64>() / (mean_volatilities.len() - 1) as f64;
            variance.sqrt()
        } else {
            0.0
        };
        
        stats.insert("avg_data_completeness".to_string(), avg_completeness);
        stats.insert("overall_mean_volatility".to_string(), overall_mean);
        stats.insert("overall_std_volatility".to_string(), overall_std);
        stats.insert("total_vectors".to_string(), self.vectors.len() as f64);
        stats.insert("unique_symbols".to_string(), self.symbols.len() as f64);
        stats.insert("total_days".to_string(), self.total_days as f64);
        
        Ok(stats)
    }
}

/// Python module exports
#[pymodule]
fn cluster_core(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_class::<FxPriceData>()?;
    m.add_class::<LogReturns>()?;
    m.add_class::<CorrelationMatrix>()?;
    m.add_class::<ClusteringResult>()?;
    m.add_class::<VolatilityProfile>()?;
    m.add_class::<ClusterStatistics>()?;
    m.add_class::<DailyVolatilityVector>()?;
    m.add_class::<DailyVolatilityVectors>()?;
    m.add_class::<VolatilityArchetype>()?;
    m.add_class::<VolatilityRegimeResult>()?;
    m.add_function(wrap_pyfunction!(calculate_log_returns, m)?)?;
    m.add_function(wrap_pyfunction!(compute_correlation_matrix, m)?)?;
    m.add_function(wrap_pyfunction!(perform_hierarchical_clustering, m)?)?;
    m.add_function(wrap_pyfunction!(calculate_cluster_statistics, m)?)?;
    m.add_function(wrap_pyfunction!(cluster_volatility_profiles, m)?)?;
    m.add_function(wrap_pyfunction!(calculate_daily_volatility_vectors, m)?)?;
    Ok(())
}

/// Compute the Pearson correlation matrix from log returns
///
/// # Arguments
/// * `log_returns` - Log returns data for multiple currency pairs
///
/// # Returns
/// * `PyResult<CorrelationMatrix>` - Correlation matrix for the currency pairs
#[pyfunction]
pub fn compute_correlation_matrix(log_returns: &LogReturns) -> PyResult<CorrelationMatrix> {
    info!("Computing correlation matrix for {} symbols", log_returns.symbols.len());
    
    let returns = &log_returns.returns_array;
    let n_symbols = returns.shape()[0];
    let n_points = returns.shape()[1];
    
    // Check for sufficient data points
    if n_points < 2 {
        error!("Insufficient data points for correlation calculation");
        return Err(CalculationError::InsufficientData.into());
    }
    
    let mut correlation_matrix = Array2::<f64>::zeros((n_symbols, n_symbols));
    
    // Compute mean and standard deviation for each time series
    let mut means = Vec::with_capacity(n_symbols);
    let mut std_devs = Vec::with_capacity(n_symbols);
    
    for i in 0..n_symbols {
        let series = returns.row(i);
        
        // Filter out NaN values
        let valid_values: Vec<f64> = series.iter()
            .filter(|&&x| !x.is_nan())
            .cloned()
            .collect();
            
        if valid_values.is_empty() {
            error!("No valid data points for symbol {}", log_returns.symbols[i]);
            return Err(CalculationError::MissingData.into());
        }
        
        let mean = valid_values.iter().sum::<f64>() / valid_values.len() as f64;
        means.push(mean);
        
        // Calculate standard deviation
        let variance = valid_values.iter()
            .map(|&x| (x - mean).powi(2))
            .sum::<f64>() / (valid_values.len() - 1) as f64;
            
        if variance.abs() < 1e-10 {
            error!("Constant time series detected for symbol {}", log_returns.symbols[i]);
            return Err(CalculationError::ConstantTimeSeries(log_returns.symbols[i].clone()).into());
        }
        
        std_devs.push(variance.sqrt());
    }
    
    // Compute correlation matrix
    for i in 0..n_symbols {
        correlation_matrix[[i, i]] = 1.0; // Diagonal elements are always 1.0
        
        for j in (i + 1)..n_symbols {
            let series_i = returns.row(i);
            let series_j = returns.row(j);
            
            // Calculate covariance using only valid pairs
            let mut covariance = 0.0;
            let mut valid_count = 0;
            
            for k in 0..n_points {
                let val_i = series_i[k];
                let val_j = series_j[k];
                
                if !val_i.is_nan() && !val_j.is_nan() {
                    covariance += (val_i - means[i]) * (val_j - means[j]);
                    valid_count += 1;
                }
            }
            
            if valid_count < 2 {
                error!("Insufficient valid pairs between {} and {}",
                      log_returns.symbols[i], log_returns.symbols[j]);
                return Err(CalculationError::InsufficientData.into());
            }
            
            covariance /= (valid_count - 1) as f64;
            
            // Calculate correlation coefficient
            let correlation = covariance / (std_devs[i] * std_devs[j]);
            
            // Ensure the correlation is between -1 and 1
            let correlation = correlation.max(-1.0).min(1.0);
            
            // Fill both sides of the symmetric matrix
            correlation_matrix[[i, j]] = correlation;
            correlation_matrix[[j, i]] = correlation;
        }
    }
    
    info!("Successfully computed correlation matrix");
    
    Ok(CorrelationMatrix {
        correlation_array: correlation_matrix,
        symbols: log_returns.symbols.clone(),
    })
}

/// Calculate logarithmic returns from price data
///
/// # Arguments
/// * `price_data` - FX price data containing prices, symbols, and timestamps
///
/// # Returns
/// * `PyResult<LogReturns>` - Log returns for each currency pair
#[pyfunction]
pub fn calculate_log_returns(price_data: &FxPriceData) -> PyResult<LogReturns> {
    info!("Calculating log returns for {} symbols", price_data.symbols.len());
    
    // Validate input data
    if price_data.prices.is_empty() || price_data.symbols.is_empty() {
        error!("Empty price data provided");
        return Err(CalculationError::MissingData.into());
    }

    let n_symbols = price_data.symbols.len();
    let data_points_per_symbol = price_data.prices.len() / n_symbols;
    
    // Prepare returns array
    let mut returns_array = Array2::<f64>::zeros((n_symbols, data_points_per_symbol - 1));
    
    // Calculate returns for each symbol
    for (symbol_idx, symbol) in price_data.symbols.iter().enumerate() {
        let start_idx = symbol_idx * data_points_per_symbol;
        let end_idx = start_idx + data_points_per_symbol;
        let prices = &price_data.prices[start_idx..end_idx];
        
        // Calculate returns for each time step
        for i in 1..data_points_per_symbol {
            let prev_price = prices[i-1];
            let curr_price = prices[i];
            
            // Handle invalid prices
            if prev_price <= 0.0 || curr_price <= 0.0 {
                warn!("Invalid price detected for symbol {}: prev={}, curr={}",
                      symbol, prev_price, curr_price);
                returns_array[[symbol_idx, i-1]] = f64::NAN;
                continue;
            }
            
            // Calculate log return
            returns_array[[symbol_idx, i-1]] = (curr_price / prev_price).ln();
        }
        
        info!("Calculated returns for symbol: {}", symbol);
    }
    
    Ok(LogReturns {
        returns_array,
        symbols: price_data.symbols.clone(),
    })
}

#[pymethods]
impl FxPriceData {
    #[new]
    fn new() -> Self {
        Self {
            prices: Vec::new(),
            symbols: Vec::new(),
            timestamps: Vec::new(),
        }
    }
}

#[pymethods]
impl ClusteringResult {
    #[new]
    fn new() -> Self {
        Self {
            cluster_assignments: Vec::new(),
            linkage_matrix: Vec::new(),
            silhouette_score: 0.0,
            cophenetic_correlation: 0.0,
        }
    }
}

#[pymethods]
impl VolatilityProfile {
    #[new]
    fn new(symbol: String) -> Self {
        Self {
            hourly_volatility: vec![0.0; 24],
            symbol,
            mean_volatility: 0.0,
        }
    }
}

/// Perform hierarchical clustering on a correlation matrix
///
/// # Arguments
/// * `correlation_matrix` - Correlation matrix for currency pairs
/// * `distance_threshold` - Threshold for cutting the dendrogram (between 0 and 1)
///
/// # Returns
/// * `PyResult<ClusteringResult>` - Clustering result containing assignments and linkage matrix
#[pyfunction]
pub fn perform_hierarchical_clustering(
    correlation_matrix: &CorrelationMatrix,
    distance_threshold: f64,
) -> PyResult<ClusteringResult> {
    info!("Starting hierarchical clustering with threshold {}", distance_threshold);

    // Validate correlation matrix
    let n = correlation_matrix.correlation_array.shape()[0];
    if n < 2 {
        error!("Insufficient data points for clustering");
        return Err(CalculationError::InsufficientData.into());
    }
    
    if correlation_matrix.correlation_array.shape()[0] != correlation_matrix.correlation_array.shape()[1] {
        error!("Correlation matrix is not square");
        return Err(CalculationError::InvalidCorrelationMatrix("Matrix is not square".into()).into());
    }

    // Convert correlation to distance matrix: distance = 1 - |correlation|
    let mut distance_matrix = Array2::<f64>::zeros((n, n));
    for i in 0..n {
        for j in 0..n {
            let corr = correlation_matrix.correlation_array[[i, j]];
            distance_matrix[[i, j]] = 1.0 - corr.abs();
        }
    }

    // Simple clustering implementation based on distance threshold
    info!("Performing simple hierarchical clustering...");
    
    // Create cluster assignments based on distance threshold
    let mut cluster_assignments = vec![0; n];
    let mut cluster_id = 0;
    
    for i in 0..n {
        if cluster_assignments[i] == 0 {
            cluster_id += 1;
            cluster_assignments[i] = cluster_id;
            
            // Find all items within distance threshold
            for j in (i + 1)..n {
                if distance_matrix[[i, j]] <= distance_threshold {
                    cluster_assignments[j] = cluster_id;
                }
            }
        }
    }
    
    // Create a simple linkage matrix (simplified for demo)
    let mut linkage_matrix = Vec::new();
    for i in 0..(n - 1) {
        linkage_matrix.push(vec![i as f64, (i + 1) as f64, distance_threshold as f64, 2.0]);
    }

    // Convert cluster assignments to 0-based indexing
    let cluster_assignments: Vec<usize> = cluster_assignments
        .into_iter()
        .map(|x| if x > 0 { x - 1 } else { 0 })
        .collect();

    // Calculate clustering quality metrics
    let silhouette = calculate_silhouette_score(&distance_matrix, &cluster_assignments);
    let cophenetic = calculate_cophenetic_correlation(&distance_matrix, &linkage_matrix);

    info!("Clustering complete. Found {} clusters",
          cluster_assignments.iter().max().unwrap_or(&0) + 1);

    Ok(ClusteringResult {
        cluster_assignments,
        linkage_matrix,
        silhouette_score: silhouette,
        cophenetic_correlation: cophenetic,
    })
}

/// Calculate detailed statistics for clusters
///
/// # Arguments
/// * `log_returns` - Log returns data for currency pairs
/// * `cluster_assignments` - Vector of cluster assignments
/// * `symbols` - Vector of currency pair symbols
/// * `cluster_id` - Optional specific cluster to calculate stats for
///
/// # Returns
/// * `PyResult<Vec<ClusterStatistics>>` - Statistics for each cluster
#[pyfunction]
#[pyo3(signature = (log_returns, cluster_assignments, symbols, cluster_id=None))]
pub fn calculate_cluster_statistics(
    log_returns: &LogReturns,
    cluster_assignments: Vec<i32>,
    symbols: Vec<String>,
    cluster_id: Option<i32>
) -> PyResult<Vec<ClusterStatistics>> {
    info!("Calculating cluster statistics");

    // Validate inputs
    if symbols.len() != cluster_assignments.len() {
        error!("Mismatch between symbols and cluster assignments lengths");
        return Err(CalculationError::InvalidCorrelationMatrix(
            "Mismatch between symbols and cluster assignments lengths".into()
        ).into());
    }

    if symbols.len() != log_returns.returns_array.shape()[0] {
        error!("Mismatch between symbols and log returns dimensions");
        return Err(CalculationError::InvalidCorrelationMatrix(
            "Mismatch between symbols and log returns dimensions".into()
        ).into());
    }

    // Get unique cluster IDs
    let mut unique_clusters: Vec<i32> = cluster_assignments.clone();
    unique_clusters.sort_unstable();
    unique_clusters.dedup();

    // Filter for specific cluster if provided
    let clusters_to_analyze = if let Some(cid) = cluster_id {
        if !unique_clusters.contains(&cid) {
            error!("Specified cluster ID {} not found", cid);
            return Err(CalculationError::ClusteringError(
                format!("Cluster ID {} not found", cid)
            ).into());
        }
        vec![cid]
    } else {
        unique_clusters
    };

    let mut cluster_stats = Vec::new();

    // Calculate statistics for each cluster
    for &cid in clusters_to_analyze.iter() {
        // Get indices of members in this cluster
        let member_indices: Vec<usize> = cluster_assignments.iter()
            .enumerate()
            .filter(|(_, &c)| c == cid)
            .map(|(i, _)| i)
            .collect();

        if member_indices.is_empty() {
            warn!("Empty cluster found: {}", cid);
            continue;
        }

        let mut stats = ClusterStatistics::new(cid);
        stats.size = member_indices.len();

        // Collect member symbols
        stats.members = member_indices.iter()
            .map(|&i| symbols[i].clone())
            .collect();

        // Calculate average volatility
        let mut total_volatility = 0.0;
        for &idx in &member_indices {
            let returns = log_returns.returns_array.row(idx);
            let volatility = returns.iter()
                .filter(|&&x| !x.is_nan())
                .map(|&x| x.powi(2))
                .sum::<f64>()
                .sqrt() * (252.0_f64).sqrt(); // Annualized volatility
            total_volatility += volatility;
        }
        stats.avg_volatility = total_volatility / member_indices.len() as f64;

        // Calculate average intra-cluster correlation
        let mut total_correlation = 0.0;
        let mut correlation_count = 0;
        
        for (i, &idx1) in member_indices.iter().enumerate() {
            for &idx2 in member_indices.iter().skip(i + 1) {
                let series1 = log_returns.returns_array.row(idx1);
                let series2 = log_returns.returns_array.row(idx2);
                
                let correlation = calculate_correlation(&series1, &series2)?;
                total_correlation += correlation;
                correlation_count += 1;
            }
        }

        stats.avg_intra_correlation = if correlation_count > 0 {
            total_correlation / correlation_count as f64
        } else {
            1.0 // Single member cluster
        };

        // Calculate cohesion score (average distance to cluster centroid)
        let mut total_distance = 0.0;
        for &idx1 in &member_indices {
            let mut member_distances = 0.0;
            for &idx2 in &member_indices {
                if idx1 != idx2 {
                    let distance = 1.0 - calculate_correlation(
                        &log_returns.returns_array.row(idx1),
                        &log_returns.returns_array.row(idx2)
                    )?;
                    member_distances += distance;
                }
            }
            if member_indices.len() > 1 {
                total_distance += member_distances / (member_indices.len() - 1) as f64;
            }
        }
        stats.cohesion_score = 1.0 - (total_distance / member_indices.len() as f64);

        cluster_stats.push(stats);
    }

    info!("Successfully calculated statistics for {} clusters", cluster_stats.len());
    Ok(cluster_stats)
}

/// Helper function to calculate correlation between two return series
fn calculate_correlation(series1: &ndarray::ArrayView1<f64>, series2: &ndarray::ArrayView1<f64>) -> PyResult<f64> {
    let mut sum_x = 0.0;
    let mut sum_y = 0.0;
    let mut sum_xy = 0.0;
    let mut sum_x2 = 0.0;
    let mut sum_y2 = 0.0;
    let mut n = 0.0;

    for (&x, &y) in series1.iter().zip(series2.iter()) {
        if x.is_nan() || y.is_nan() {
            continue;
        }
        sum_x += x;
        sum_y += y;
        sum_xy += x * y;
        sum_x2 += x * x;
        sum_y2 += y * y;
        n += 1.0;
    }

    if n < 2.0 {
        error!("Insufficient valid data points for correlation calculation");
        return Err(CalculationError::InsufficientData.into());
    }

    let numerator = sum_xy - (sum_x * sum_y) / n;
    let denominator = ((sum_x2 - (sum_x * sum_x) / n) * (sum_y2 - (sum_y * sum_y) / n)).sqrt();

    if denominator.abs() < 1e-10 {
        error!("Constant time series detected in correlation calculation");
        return Err(CalculationError::ConstantTimeSeries("Series has zero variance".into()).into());
    }

    Ok((numerator / denominator).max(-1.0).min(1.0))
}

// Helper function to calculate silhouette score
fn calculate_silhouette_score(distance_matrix: &Array2<f64>, cluster_assignments: &[usize]) -> f64 {
    let n = cluster_assignments.len();
    if n < 2 {
        return 0.0;
    }

    let mut silhouette_sum = 0.0;
    let mut valid_points = 0;

    for i in 0..n {
        let cluster_i = cluster_assignments[i];
        
        // Calculate a(i): average distance to points in same cluster
        let mut same_cluster_dist_sum = 0.0;
        let mut same_cluster_count = 0;
        
        // Calculate b(i): minimum average distance to points in different clusters
        let mut min_diff_cluster_avg = f64::INFINITY;
        let mut cluster_counts = std::collections::HashMap::new();
        
        for j in 0..n {
            if i == j {
                continue;
            }
            
            let cluster_j = cluster_assignments[j];
            *cluster_counts.entry(cluster_j).or_insert(0) += 1;
            
            if cluster_j == cluster_i {
                same_cluster_dist_sum += distance_matrix[[i, j]];
                same_cluster_count += 1;
            }
        }
        
        // Skip points that are alone in their cluster
        if same_cluster_count == 0 {
            continue;
        }
        
        let a_i = same_cluster_dist_sum / same_cluster_count as f64;
        
        // Calculate average distance to each different cluster
        for (other_cluster, count) in cluster_counts {
            if other_cluster == cluster_i {
                continue;
            }
            
            let mut diff_cluster_dist_sum = 0.0;
            for j in 0..n {
                if cluster_assignments[j] == other_cluster {
                    diff_cluster_dist_sum += distance_matrix[[i, j]];
                }
            }
            
            let avg_dist = diff_cluster_dist_sum / count as f64;
            min_diff_cluster_avg = min_diff_cluster_avg.min(avg_dist);
        }
        
        let b_i = min_diff_cluster_avg;
        let max_ab = a_i.max(b_i);
        
        if max_ab > 0.0 {
            silhouette_sum += (b_i - a_i) / max_ab;
            valid_points += 1;
        }
    }
    
    if valid_points > 0 {
        silhouette_sum / valid_points as f64
    } else {
        0.0
    }
}

// Helper function to calculate cophenetic correlation
fn calculate_cophenetic_correlation(distance_matrix: &Array2<f64>, linkage_matrix: &[Vec<f64>]) -> f64 {
    let n = distance_matrix.shape()[0];
    if n < 2 {
        return 0.0;
    }

    // Build cophenetic distance matrix from linkage matrix
    let mut cophenetic_distances = Array2::zeros((n, n));
    let mut cluster_mapping = vec![vec![]; n];
    
    // Initialize each point as its own cluster
    for i in 0..n {
        cluster_mapping[i] = vec![i];
    }
    
    // Process linkage matrix to build cophenetic distances
    for (idx, link) in linkage_matrix.iter().enumerate() {
        if link.len() < 4 {
            continue;
        }
        
        let cluster1_idx = link[0] as usize;
        let cluster2_idx = link[1] as usize;
        let distance = link[2];
        
        // Get all points in both clusters
        let cluster1 = &cluster_mapping[cluster1_idx];
        let cluster2 = &cluster_mapping[cluster2_idx];
        
        // Fill distances between all points in the clusters
        for &i in cluster1.iter() {
            for &j in cluster2.iter() {
                cophenetic_distances[[i, j]] = distance;
                cophenetic_distances[[j, i]] = distance;
            }
        }
        
        // Merge clusters
        let new_cluster_idx = n + idx;
        let mut merged = cluster1.clone();
        merged.extend(cluster2);
        if new_cluster_idx < cluster_mapping.len() {
            cluster_mapping[new_cluster_idx] = merged;
        }
    }
    
    // Calculate correlation between original and cophenetic distances
    let mut sum_xy = 0.0;
    let mut sum_x = 0.0;
    let mut sum_y = 0.0;
    let mut sum_x2 = 0.0;
    let mut sum_y2 = 0.0;
    let mut count = 0;
    
    for i in 0..n {
        for j in (i+1)..n {
            let x = distance_matrix[[i, j]];
            let y = cophenetic_distances[[i, j]];

            sum_xy += x * y;
            sum_x += x;
            sum_y += y;
            sum_x2 += x * x;
            sum_y2 += y * y;
            count += 1;
        }
    }

    if count == 0 {
        return 0.0;
    }

    let n_f = count as f64;
    let numerator = n_f * sum_xy - sum_x * sum_y;
    let denominator = ((n_f * sum_x2 - sum_x * sum_x) * (n_f * sum_y2 - sum_y * sum_y)).sqrt();

    if denominator == 0.0 {
        0.0
    } else {
        numerator / denominator
    }
}

/// Calculate daily volatility vectors from price data
///
/// # Arguments
/// * `price_data` - FX price data containing prices, symbols, and timestamps
/// * `timezone_offset` - Timezone offset in hours (default: 0 for UTC)
///
/// # Returns
/// * `PyResult<DailyVolatilityVectors>` - Collection of daily volatility vectors
#[pyfunction]
#[pyo3(signature = (price_data, timezone_offset=0))]
pub fn calculate_daily_volatility_vectors(
    price_data: &FxPriceData,
    timezone_offset: i32,
) -> PyResult<DailyVolatilityVectors> {
    info!("Calculating daily volatility vectors for {} symbols with timezone offset {}",
          price_data.symbols.len(), timezone_offset);

    let mut vectors = Vec::new();
    let mut all_dates = std::collections::HashSet::new();

    // Assume data is structured as: [price1, price2, ..., priceN] for each timestamp
    // where N is the number of symbols
    let n_symbols = price_data.symbols.len();
    if n_symbols == 0 {
        return Err(PyValueError::new_err("No symbols provided"));
    }

    let n_timestamps = price_data.timestamps.len();
    let expected_prices = n_timestamps * n_symbols;

    if price_data.prices.len() != expected_prices {
        return Err(PyValueError::new_err(format!(
            "Price data length {} doesn't match expected {} (timestamps: {}, symbols: {})",
            price_data.prices.len(), expected_prices, n_timestamps, n_symbols
        )));
    }

    // Group data by symbol and date
    for (symbol_idx, symbol) in price_data.symbols.iter().enumerate() {
        let mut daily_groups: std::collections::HashMap<String, Vec<(i64, f64)>> = std::collections::HashMap::new();

        // Extract data for this symbol
        for (time_idx, &timestamp) in price_data.timestamps.iter().enumerate() {
            let price_idx = time_idx * n_symbols + symbol_idx;
            if price_idx < price_data.prices.len() {
                let price = price_data.prices[price_idx];

                // Convert timestamp to date string with timezone offset
                let adjusted_timestamp = timestamp + (timezone_offset as i64 * 3600);
                let datetime = chrono::DateTime::from_timestamp(adjusted_timestamp, 0)
                    .ok_or_else(|| PyValueError::new_err("Invalid timestamp"))?;
                let date_str = datetime.format("%Y-%m-%d").to_string();

                daily_groups.entry(date_str.clone()).or_insert_with(Vec::new).push((timestamp, price));
                all_dates.insert(date_str);
            }
        }

        // Calculate volatility vector for each day
        for (date, day_data) in daily_groups {
            if day_data.len() < 2 {
                continue; // Need at least 2 data points
            }

            let volatility_vector = calculate_hourly_volatility_from_prices(&day_data, timezone_offset)?;

            // Calculate statistics
            let valid_values: Vec<f64> = volatility_vector.iter().filter(|&&v| !v.is_nan() && v > 0.0).cloned().collect();
            let data_completeness = valid_values.len() as f64 / 24.0;

            // Only include days with reasonable data completeness
            if data_completeness >= 0.5 && !valid_values.is_empty() {
                let mean_volatility = valid_values.iter().sum::<f64>() / valid_values.len() as f64;
                let variance = valid_values.iter().map(|v| (v - mean_volatility).powi(2)).sum::<f64>() / valid_values.len() as f64;
                let std_volatility = variance.sqrt();

                // Find peak and quiet hours
                let (peak_hour, quiet_hour) = find_peak_and_quiet_hours(&volatility_vector);

                vectors.push(DailyVolatilityVector {
                    symbol: symbol.clone(),
                    date: date.clone(),
                    timestamp: day_data[0].0, // Use first timestamp of the day
                    hourly_volatility: volatility_vector.to_vec(),
                    mean_volatility,
                    std_volatility,
                    peak_hour,
                    quiet_hour,
                    data_completeness,
                });
            }
        }
    }

    info!("Generated {} daily volatility vectors", vectors.len());

    let mut date_range: Vec<String> = all_dates.into_iter().collect();
    date_range.sort();
    let total_days = date_range.len();

    let avg_data_completeness = if vectors.is_empty() { 0.0 } else {
        vectors.iter().map(|v| v.data_completeness).sum::<f64>() / vectors.len() as f64
    };

    Ok(DailyVolatilityVectors {
        vectors,
        symbols: price_data.symbols.clone(),
        date_range,
        total_days,
        avg_data_completeness,
        timezone: format!("UTC{:+}", timezone_offset),
    })
}

/// Calculate hourly volatility from price data with timestamps
fn calculate_hourly_volatility_from_prices(day_data: &[(i64, f64)], timezone_offset: i32) -> PyResult<[f64; 24]> {
    let mut hourly_volatility = [f64::NAN; 24];

    // Group data by hour
    let mut hourly_groups: std::collections::HashMap<usize, Vec<f64>> = std::collections::HashMap::new();

    for &(timestamp, price) in day_data {
        let adjusted_timestamp = timestamp + (timezone_offset as i64 * 3600);
        let datetime = chrono::DateTime::from_timestamp(adjusted_timestamp, 0)
            .ok_or_else(|| PyValueError::new_err("Invalid timestamp"))?;
        let hour = datetime.hour() as usize;

        if hour < 24 && price > 0.0 {
            hourly_groups.entry(hour).or_insert_with(Vec::new).push(price);
        }
    }

    // Calculate volatility for each hour
    for (hour, prices) in hourly_groups {
        if prices.len() >= 2 {
            // Calculate returns within the hour
            let mut returns = Vec::new();
            for i in 1..prices.len() {
                let prev_price = prices[i-1];
                let curr_price = prices[i];

                if prev_price > 0.0 && curr_price > 0.0 {
                    let return_val = (curr_price / prev_price).ln();
                    returns.push(return_val);
                }
            }

            // Calculate standard deviation of returns (volatility)
            if returns.len() >= 2 {
                let mean = returns.iter().sum::<f64>() / returns.len() as f64;
                let variance = returns.iter()
                    .map(|r| (r - mean).powi(2))
                    .sum::<f64>() / (returns.len() - 1) as f64;

                hourly_volatility[hour] = variance.sqrt();
            }
        }
    }

    Ok(hourly_volatility)
}

/// Find peak and quiet hours from volatility pattern
fn find_peak_and_quiet_hours(volatility_vector: &[f64]) -> (usize, usize) {
    let mut peak_hour = 0;
    let mut quiet_hour = 0;
    let mut max_vol = f64::NEG_INFINITY;
    let mut min_vol = f64::INFINITY;

    for (hour, &vol) in volatility_vector.iter().enumerate() {
        if !vol.is_nan() && vol.is_finite() {
            if vol > max_vol {
                max_vol = vol;
                peak_hour = hour;
            }
            if vol < min_vol {
                min_vol = vol;
                quiet_hour = hour;
            }
        }
    }

    (peak_hour, quiet_hour)
}

/// Calculate hourly volatility from intraday price data (legacy function)
fn calculate_hourly_volatility(day_data: &[&Vec<f64>]) -> PyResult<[f64; 24]> {
    let mut hourly_volatility = [f64::NAN; 24];

    // Group data by hour
    let mut hourly_groups: std::collections::HashMap<usize, Vec<&Vec<f64>>> = std::collections::HashMap::new();

    for row in day_data {
        let timestamp = row[1] as i64;
        let datetime = chrono::DateTime::from_timestamp(timestamp, 0)
            .ok_or_else(|| PyValueError::new_err("Invalid timestamp"))?;
        let hour = datetime.hour() as usize;

        if hour < 24 {
            hourly_groups.entry(hour).or_insert_with(Vec::new).push(row);
        }
    }

    // Calculate volatility for each hour
    for (hour, hour_data) in hourly_groups {
        if hour_data.len() >= 2 {
            // Calculate returns within the hour
            let mut returns = Vec::new();
            for i in 1..hour_data.len() {
                let prev_close = hour_data[i-1][5]; // Close price
                let curr_close = hour_data[i][5];   // Close price

                if prev_close > 0.0 && curr_close > 0.0 {
                    let return_val = (curr_close / prev_close).ln();
                    returns.push(return_val);
                }
            }

            // Calculate standard deviation of returns (volatility)
            if returns.len() >= 2 {
                let mean = returns.iter().sum::<f64>() / returns.len() as f64;
                let variance = returns.iter()
                    .map(|r| (r - mean).powi(2))
                    .sum::<f64>() / (returns.len() - 1) as f64;

                hourly_volatility[hour] = variance.sqrt();
            }
        }
    }

    Ok(hourly_volatility)
}

/// Cluster daily volatility profiles into distinct volatility regimes using K-means
///
/// # Arguments
/// * `daily_volatility_vectors` - Collection of daily volatility vectors with 24-hour patterns
/// * `n_clusters` - Number of volatility regimes to identify
/// * `max_iterations` - Maximum number of K-means iterations (default: 300)
/// * `tolerance` - Convergence tolerance for K-means (default: 1e-4)
/// * `random_seed` - Random seed for reproducible results (default: 42)
///
/// # Returns
/// * `PyResult<VolatilityRegimeResult>` - Clustering result with regime assignments and archetypes
#[pyfunction]
#[pyo3(signature = (daily_volatility_vectors, n_clusters, max_iterations=300, tolerance=1e-4, random_seed=42))]
pub fn cluster_volatility_profiles(
    daily_volatility_vectors: &DailyVolatilityVectors,
    n_clusters: usize,
    max_iterations: Option<usize>,
    tolerance: Option<f64>,
    random_seed: Option<u64>,
) -> PyResult<VolatilityRegimeResult> {
    let start_time = std::time::Instant::now();
    
    info!("Starting volatility regime clustering with {} clusters", n_clusters);
    info!("Input data: {} vectors across {} symbols", 
          daily_volatility_vectors.vectors.len(), 
          daily_volatility_vectors.symbols.len());
    
    // Input validation
    if daily_volatility_vectors.vectors.is_empty() {
        error!("No volatility vectors provided for clustering");
        return Err(CalculationError::MissingData.into());
    }
    
    let n_observations = daily_volatility_vectors.vectors.len();
    
    if n_clusters == 0 {
        error!("Number of clusters must be greater than 0");
        return Err(CalculationError::ClusteringError("n_clusters must be > 0".into()).into());
    }
    
    if n_clusters > n_observations {
        error!("Number of clusters ({}) cannot exceed number of observations ({})", 
               n_clusters, n_observations);
        return Err(CalculationError::ClusteringError(
            format!("n_clusters ({}) > n_observations ({})", n_clusters, n_observations)
        ).into());
    }
    
    if n_clusters == 1 {
        info!("Single cluster requested, creating trivial clustering");
        return create_single_cluster_result(daily_volatility_vectors, start_time);
    }
    
    // Data quality validation
    let mut valid_vectors = Vec::new();
    let mut valid_indices = Vec::new();
    
    for (idx, vector) in daily_volatility_vectors.vectors.iter().enumerate() {
        if validate_volatility_vector(vector)? {
            valid_vectors.push(vector);
            valid_indices.push(idx);
        } else {
            warn!("Skipping invalid volatility vector for {} on {}", 
                  vector.symbol, vector.date);
        }
    }
    
    if valid_vectors.is_empty() {
        error!("No valid volatility vectors found after validation");
        return Err(CalculationError::ClusteringError("No valid data for clustering".into()).into());
    }
    
    if valid_vectors.len() < n_clusters {
        error!("Insufficient valid vectors ({}) for {} clusters", 
               valid_vectors.len(), n_clusters);
        return Err(CalculationError::ClusteringError(
            format!("Insufficient valid vectors ({}) for {} clusters", 
                   valid_vectors.len(), n_clusters)
        ).into());
    }
    
    info!("Using {} valid vectors out of {} total", valid_vectors.len(), n_observations);
    
    // Convert volatility vectors to ndarray for clustering
    let data_matrix = vectors_to_matrix(&valid_vectors)?;
    
    // Normalize the data for better clustering performance
    let (normalized_matrix, normalization_stats) = normalize_volatility_data(&data_matrix)?;
    
    info!("Data matrix shape: {:?}", normalized_matrix.shape());
    
    // Configure K-means clustering
    let max_iter = max_iterations.unwrap_or(300);
    let tol = tolerance.unwrap_or(1e-4);
    let seed = random_seed.unwrap_or(42);
    
    info!("K-means configuration: max_iter={}, tolerance={}, seed={}", 
          max_iter, tol, seed);
    
    // Perform K-means clustering
    let kmeans_result = perform_kmeans_clustering(
        &normalized_matrix, 
        n_clusters, 
        max_iter, 
        tol, 
        seed
    )?;
    
    // Calculate cluster quality metrics
    let silhouette_score = calculate_volatility_silhouette_score(
        &normalized_matrix, 
        &kmeans_result.assignments
    )?;
    
    let calinski_harabasz_score = calculate_calinski_harabasz_score(
        &normalized_matrix, 
        &kmeans_result.assignments
    )?;
    
    // Create archetypes from centroids
    let archetypes = create_volatility_archetypes(
        &kmeans_result.centroids,
        &kmeans_result.assignments,
        &valid_vectors,
        &normalization_stats,
    )?;
    
    // Map cluster assignments back to original indices
    let mut full_assignments = vec![0; n_observations];
    for (valid_idx, &cluster_id) in valid_indices.iter().zip(kmeans_result.assignments.iter()) {
        full_assignments[*valid_idx] = cluster_id;
    }
    
    let processing_time = start_time.elapsed().as_millis() as u64;
    
    let result = VolatilityRegimeResult {
        cluster_assignments: full_assignments,
        archetypes,
        n_clusters,
        within_cluster_sum_of_squares: kmeans_result.wcss,
        total_sum_of_squares: kmeans_result.tss,
        silhouette_score,
        calinski_harabasz_score,
        inertia: kmeans_result.inertia,
        n_iterations: kmeans_result.n_iterations,
        converged: kmeans_result.converged,
        processing_time_ms: processing_time,
    };
    
    info!("Volatility regime clustering completed in {}ms", processing_time);
    info!("Clustering quality - Silhouette: {:.4}, Calinski-Harabasz: {:.4}", 
          silhouette_score, calinski_harabasz_score);
    info!("Convergence: {}, Iterations: {}", result.converged, result.n_iterations);
    
    Ok(result)
}

/// Helper struct for K-means clustering results
#[derive(Debug)]
struct KMeansResult {
    assignments: Vec<usize>,
    centroids: Array2<f64>,
    wcss: f64,
    tss: f64,
    inertia: f64,
    n_iterations: usize,
    converged: bool,
}

/// Helper struct for data normalization statistics
#[derive(Debug)]
struct NormalizationStats {
    means: Array1<f64>,
    std_devs: Array1<f64>,
}

/// Create a single cluster result when n_clusters = 1
fn create_single_cluster_result(
    daily_volatility_vectors: &DailyVolatilityVectors,
    start_time: std::time::Instant,
) -> PyResult<VolatilityRegimeResult> {
    let n_observations = daily_volatility_vectors.vectors.len();
    let assignments = vec![0; n_observations];
    
    // Calculate overall centroid
    let mut centroid = vec![0.0; 24];
    let mut valid_count = 0;
    
    for vector in &daily_volatility_vectors.vectors {
        if validate_volatility_vector(vector)? {
            for (i, &val) in vector.hourly_volatility.iter().enumerate() {
                if !val.is_nan() && val.is_finite() {
                    centroid[i] += val;
                }
            }
            valid_count += 1;
        }
    }
    
    if valid_count > 0 {
        for val in centroid.iter_mut() {
            *val /= valid_count as f64;
        }
    }
    
    // Find peak and quiet hours
    let (peak_hour, quiet_hour) = find_peak_and_quiet_hours(&centroid);
    let mean_volatility = centroid.iter().sum::<f64>() / 24.0;
    let std_volatility = {
        let variance = centroid.iter()
            .map(|&x| (x - mean_volatility).powi(2))
            .sum::<f64>() / 24.0;
        variance.sqrt()
    };
    
    let archetype = VolatilityArchetype {
        regime_id: 0,
        hourly_pattern: centroid,
        mean_volatility,
        std_volatility,
        peak_hour,
        quiet_hour,
        regime_name: "Single_Regime".to_string(),
        member_count: n_observations,
        intra_regime_coherence: 1.0,
    };
    
    let processing_time = start_time.elapsed().as_millis() as u64;
    
    Ok(VolatilityRegimeResult {
        cluster_assignments: assignments,
        archetypes: vec![archetype],
        n_clusters: 1,
        within_cluster_sum_of_squares: 0.0,
        total_sum_of_squares: 0.0,
        silhouette_score: 0.0,
        calinski_harabasz_score: 0.0,
        inertia: 0.0,
        n_iterations: 0,
        converged: true,
        processing_time_ms: processing_time,
    })
}

/// Validate a volatility vector for clustering
fn validate_volatility_vector(vector: &DailyVolatilityVector) -> PyResult<bool> {
    // Check data completeness threshold
    if vector.data_completeness < 0.5 {
        return Ok(false);
    }
    
    // Check for sufficient non-NaN values
    let valid_count = vector.hourly_volatility.iter()
        .filter(|&&x| !x.is_nan() && x.is_finite() && x >= 0.0)
        .count();
    
    if valid_count < 12 { // At least 50% of hours
        return Ok(false);
    }
    
    // Check for extreme outliers
    let valid_values: Vec<f64> = vector.hourly_volatility.iter()
        .filter(|&&x| !x.is_nan() && x.is_finite() && x >= 0.0)
        .cloned()
        .collect();
    
    if valid_values.is_empty() {
        return Ok(false);
    }
    
    let mean = valid_values.iter().sum::<f64>() / valid_values.len() as f64;
    let std_dev = {
        let variance = valid_values.iter()
            .map(|&x| (x - mean).powi(2))
            .sum::<f64>() / valid_values.len() as f64;
        variance.sqrt()
    };
    
    // Check for extreme outliers (more than 5 standard deviations)
    let outlier_threshold = mean + 5.0 * std_dev;
    let extreme_outliers = valid_values.iter()
        .filter(|&&x| x > outlier_threshold)
        .count();
    
    if extreme_outliers > valid_values.len() / 4 { // More than 25% outliers
        return Ok(false);
    }
    
    Ok(true)
}

/// Convert volatility vectors to matrix for clustering
fn vectors_to_matrix(vectors: &[&DailyVolatilityVector]) -> PyResult<Array2<f64>> {
    let n_vectors = vectors.len();
    let n_features = 24;
    
    let mut matrix = Array2::zeros((n_vectors, n_features));
    
    for (i, vector) in vectors.iter().enumerate() {
        for (j, &val) in vector.hourly_volatility.iter().enumerate() {
            // Fill NaN values with the mean of the same hour across all vectors
            if val.is_nan() || !val.is_finite() {
                let hour_values: Vec<f64> = vectors.iter()
                    .map(|v| v.hourly_volatility[j])
                    .filter(|&x| !x.is_nan() && x.is_finite())
                    .collect();
                
                let hour_mean = if hour_values.is_empty() {
                    0.0
                } else {
                    hour_values.iter().sum::<f64>() / hour_values.len() as f64
                };
                
                matrix[[i, j]] = hour_mean;
            } else {
                matrix[[i, j]] = val;
            }
        }
    }
    
    Ok(matrix)
}

/// Normalize volatility data for better clustering performance
fn normalize_volatility_data(data: &Array2<f64>) -> PyResult<(Array2<f64>, NormalizationStats)> {
    let n_features = data.shape()[1];
    let mut means = Array1::zeros(n_features);
    let mut std_devs = Array1::zeros(n_features);
    
    // Calculate means and standard deviations for each feature (hour)
    for j in 0..n_features {
        let column = data.column(j);
        let mean = column.mean().unwrap_or(0.0);
        means[j] = mean;
        
        let variance = column.iter()
            .map(|&x| (x - mean).powi(2))
            .sum::<f64>() / column.len() as f64;
        
        let std_dev = variance.sqrt();
        std_devs[j] = if std_dev > 1e-10 { std_dev } else { 1.0 };
    }
    
    // Normalize the data
    let mut normalized = data.clone();
    for j in 0..n_features {
        let mut column = normalized.column_mut(j);
        column.iter_mut().for_each(|x| *x = (*x - means[j]) / std_devs[j]);
    }
    
    let stats = NormalizationStats { means, std_devs };
    Ok((normalized, stats))
}

/// Perform K-means clustering using linfa
fn perform_kmeans_clustering(
    data: &Array2<f64>,
    n_clusters: usize,
    max_iterations: usize,
    tolerance: f64,
    seed: u64,
) -> PyResult<KMeansResult> {
    use linfa::Dataset;
    use rand::SeedableRng;
    use rand_isaac::Isaac64Rng;
    
    let targets = Array1::<usize>::zeros(data.shape()[0]);
    let dataset = Dataset::new(data.clone(), targets);
    
    let rng = Isaac64Rng::seed_from_u64(seed);
    let kmeans = KMeans::params_with_rng(n_clusters, rng)
        .max_n_iterations(max_iterations as u64)
        .tolerance(tolerance)
        .fit(&dataset)
        .map_err(|e| {
            error!("K-means clustering failed: {}", e);
            CalculationError::ClusteringError(format!("K-means failed: {}", e))
        })?;
    
    let assignments = kmeans.predict(&dataset).to_vec();
    let centroids = kmeans.centroids().clone();
    
    // Calculate within-cluster sum of squares (WCSS)
    let mut wcss = 0.0;
    let mut tss = 0.0;
    
    let data_mean = data.mean_axis(ndarray::Axis(0)).unwrap();
    
    for (i, &cluster_id) in assignments.iter().enumerate() {
        let point = data.row(i);
        let centroid = centroids.row(cluster_id);
        
        // Within-cluster sum of squares
        let cluster_distance = point.iter()
            .zip(centroid.iter())
            .map(|(&x, &c)| (x - c).powi(2))
            .sum::<f64>();
        wcss += cluster_distance;
        
        // Total sum of squares
        let total_distance = point.iter()
            .zip(data_mean.iter())
            .map(|(&x, &m)| (x - m).powi(2))
            .sum::<f64>();
        tss += total_distance;
    }
    
    let inertia = wcss; // Inertia is the same as WCSS in K-means
    let n_iterations = max_iterations; // linfa doesn't expose actual iterations
    let converged = true; // Assume convergence for now
    
    Ok(KMeansResult {
        assignments,
        centroids,
        wcss,
        tss,
        inertia,
        n_iterations,
        converged,
    })
}

/// Calculate silhouette score for volatility clustering
fn calculate_volatility_silhouette_score(
    data: &Array2<f64>,
    assignments: &[usize],
) -> PyResult<f64> {
    let n_points = data.shape()[0];
    if n_points < 2 {
        return Ok(0.0);
    }
    
    let mut silhouette_sum = 0.0;
    let mut valid_points = 0;
    
    for i in 0..n_points {
        let cluster_i = assignments[i];
        
        // Calculate a(i): average distance to points in same cluster
        let mut same_cluster_distances = Vec::new();
        for j in 0..n_points {
            if i != j && assignments[j] == cluster_i {
                let distance = euclidean_distance(&data.row(i), &data.row(j));
                same_cluster_distances.push(distance);
            }
        }
        
        if same_cluster_distances.is_empty() {
            continue; // Skip singleton clusters
        }
        
        let a_i = same_cluster_distances.iter().sum::<f64>() / same_cluster_distances.len() as f64;
        
        // Calculate b(i): minimum average distance to points in other clusters
        let mut min_avg_distance = f64::INFINITY;
        let unique_clusters: std::collections::HashSet<usize> = assignments.iter().cloned().collect();
        
        for &other_cluster in &unique_clusters {
            if other_cluster == cluster_i {
                continue;
            }
            
            let mut other_cluster_distances = Vec::new();
            for j in 0..n_points {
                if assignments[j] == other_cluster {
                    let distance = euclidean_distance(&data.row(i), &data.row(j));
                    other_cluster_distances.push(distance);
                }
            }
            
            if !other_cluster_distances.is_empty() {
                let avg_distance = other_cluster_distances.iter().sum::<f64>() / other_cluster_distances.len() as f64;
                min_avg_distance = min_avg_distance.min(avg_distance);
            }
        }
        
        let b_i = min_avg_distance;
        let max_ab = a_i.max(b_i);
        
        if max_ab > 0.0 {
            silhouette_sum += (b_i - a_i) / max_ab;
            valid_points += 1;
        }
    }
    
    Ok(if valid_points > 0 {
        silhouette_sum / valid_points as f64
    } else {
        0.0
    })
}

/// Calculate Calinski-Harabasz score
fn calculate_calinski_harabasz_score(
    data: &Array2<f64>,
    assignments: &[usize],
) -> PyResult<f64> {
    let n_points = data.shape()[0];
    let n_clusters = assignments.iter().max().unwrap_or(&0) + 1;
    
    if n_clusters <= 1 || n_points <= n_clusters {
        return Ok(0.0);
    }
    
    let overall_centroid = data.mean_axis(ndarray::Axis(0)).unwrap();
    
    // Calculate between-cluster sum of squares
    let mut between_ss = 0.0;
    let mut within_ss = 0.0;
    
    for cluster_id in 0..n_clusters {
        let cluster_points: Vec<_> = (0..n_points)
            .filter(|&i| assignments[i] == cluster_id)
            .collect();
        
        if cluster_points.is_empty() {
            continue;
        }
        
        // Calculate cluster centroid
        let cluster_size = cluster_points.len();
        let mut cluster_centroid = Array1::<f64>::zeros(data.shape()[1]);
        for &point_idx in &cluster_points {
            cluster_centroid = &cluster_centroid + &data.row(point_idx);
        }
        cluster_centroid = &cluster_centroid / (cluster_size as f64);
        
        // Between-cluster sum of squares
        let centroid_distance = cluster_centroid.iter()
            .zip(overall_centroid.iter())
            .map(|(&c, &o)| (c - o).powi(2))
            .sum::<f64>();
        between_ss += cluster_size as f64 * centroid_distance;
        
        // Within-cluster sum of squares
        for &point_idx in &cluster_points {
            let point_distance = data.row(point_idx).iter()
                .zip(cluster_centroid.iter())
                .map(|(&p, &c)| (p - c).powi(2))
                .sum::<f64>();
            within_ss += point_distance;
        }
    }
    
    let ch_score = if within_ss > 0.0 {
        (between_ss / (n_clusters - 1) as f64) / (within_ss / (n_points - n_clusters) as f64)
    } else {
        0.0
    };
    
    Ok(ch_score)
}

/// Calculate Euclidean distance between two points
fn euclidean_distance(point1: &ndarray::ArrayView1<f64>, point2: &ndarray::ArrayView1<f64>) -> f64 {
    point1.iter()
        .zip(point2.iter())
        .map(|(&x, &y)| (x - y).powi(2))
        .sum::<f64>()
        .sqrt()
}

/// Create volatility archetypes from centroids
fn create_volatility_archetypes(
    centroids: &Array2<f64>,
    assignments: &[usize],
    vectors: &[&DailyVolatilityVector],
    normalization_stats: &NormalizationStats,
) -> PyResult<Vec<VolatilityArchetype>> {
    let n_clusters = centroids.shape()[0];
    let mut archetypes = Vec::new();
    
    for cluster_id in 0..n_clusters {
        // Denormalize centroid
        let normalized_centroid = centroids.row(cluster_id);
        let mut hourly_pattern = vec![0.0; 24];
        
        for (i, &norm_val) in normalized_centroid.iter().enumerate() {
            hourly_pattern[i] = norm_val * normalization_stats.std_devs[i] + normalization_stats.means[i];
        }
        
        let mean_volatility = hourly_pattern.iter().sum::<f64>() / 24.0;
        let std_volatility = {
            let variance = hourly_pattern.iter()
                .map(|&x| (x - mean_volatility).powi(2))
                .sum::<f64>() / 24.0;
            variance.sqrt()
        };
        
        let (peak_hour, quiet_hour) = find_peak_and_quiet_hours(&hourly_pattern);
        
        // Count members in this cluster
        let member_count = assignments.iter().filter(|&&id| id == cluster_id).count();
        
        // Calculate intra-cluster coherence
        let cluster_members: Vec<_> = assignments.iter()
            .enumerate()
            .filter(|(_, &id)| id == cluster_id)
            .map(|(idx, _)| idx)
            .collect();
        
        let intra_regime_coherence = if cluster_members.len() > 1 {
            calculate_intra_cluster_coherence(&cluster_members, vectors)?
        } else {
            1.0
        };
        
        let regime_name = classify_regime_pattern(&hourly_pattern, peak_hour, quiet_hour);
        
        let archetype = VolatilityArchetype {
            regime_id: cluster_id,
            hourly_pattern,
            mean_volatility,
            std_volatility,
            peak_hour,
            quiet_hour,
            regime_name,
            member_count,
            intra_regime_coherence,
        };
        
        archetypes.push(archetype);
    }
    
    Ok(archetypes)
}



/// Calculate intra-cluster coherence
fn calculate_intra_cluster_coherence(
    cluster_members: &[usize],
    vectors: &[&DailyVolatilityVector],
) -> PyResult<f64> {
    if cluster_members.len() < 2 {
        return Ok(1.0);
    }
    
    let mut correlation_sum = 0.0;
    let mut correlation_count = 0;
    
    for i in 0..cluster_members.len() {
        for j in (i + 1)..cluster_members.len() {
            let vec1 = &vectors[cluster_members[i]].hourly_volatility;
            let vec2 = &vectors[cluster_members[j]].hourly_volatility;
            
            let correlation = calculate_vector_correlation(vec1, vec2)?;
            correlation_sum += correlation;
            correlation_count += 1;
        }
    }
    
    Ok(if correlation_count > 0 {
        correlation_sum / correlation_count as f64
    } else {
        1.0
    })
}

/// Calculate correlation between two volatility vectors
fn calculate_vector_correlation(vec1: &[f64], vec2: &[f64]) -> PyResult<f64> {
    let mut sum1 = 0.0;
    let mut sum2 = 0.0;
    let mut sum12 = 0.0;
    let mut sum1_sq = 0.0;
    let mut sum2_sq = 0.0;
    let mut n = 0;
    
    for (&x, &y) in vec1.iter().zip(vec2.iter()) {
        if !x.is_nan() && !y.is_nan() && x.is_finite() && y.is_finite() {
            sum1 += x;
            sum2 += y;
            sum12 += x * y;
            sum1_sq += x * x;
            sum2_sq += y * y;
            n += 1;
        }
    }
    
    if n < 2 {
        return Ok(0.0);
    }
    
    let n_f = n as f64;
    let numerator = sum12 - (sum1 * sum2) / n_f;
    let denominator = ((sum1_sq - sum1 * sum1 / n_f) * (sum2_sq - sum2 * sum2 / n_f)).sqrt();
    
    if denominator > 1e-10 {
        Ok((numerator / denominator).max(-1.0).min(1.0))
    } else {
        Ok(0.0)
    }
}

/// Classify regime pattern based on volatility characteristics
fn classify_regime_pattern(hourly_pattern: &[f64], peak_hour: usize, _quiet_hour: usize) -> String {
    let mean_vol = hourly_pattern.iter().sum::<f64>() / 24.0;
    let std_vol = {
        let variance = hourly_pattern.iter()
            .map(|&x| (x - mean_vol).powi(2))
            .sum::<f64>() / 24.0;
        variance.sqrt()
    };
    
    let coefficient_of_variation = if mean_vol > 0.0 { std_vol / mean_vol } else { 0.0 };
    
    // Classify based on peak hour and volatility characteristics
    let regime_type = match peak_hour {
        0..=4 => "Asia_Pacific",
        5..=11 => "European",
        12..=17 => "US_Markets",
        18..=23 => "After_Hours",
        _ => "Unknown",
    };
    
    let intensity = if coefficient_of_variation > 0.5 {
        "High_Variation"
    } else if coefficient_of_variation > 0.3 {
        "Medium_Variation"
    } else {
        "Low_Variation"
    };
    
    format!("{}_{}", regime_type, intensity)
}
